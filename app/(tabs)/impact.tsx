import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import Card from '../../components/ui/Card';

import { textStyles } from '../../lib/design';

import { useState } from 'react';
import GradientBackground from '../../components/GradientBackground';
import AppTopBar from '../../components/AppTopBar';
import { tokens } from '../../lib/design/tokens';

import Ionicons from '@expo/vector-icons/Ionicons';
// Removed BlurView import
// Removed unused design system imports

interface StatCardProps {
  title: string;
  value: string;
  subtitle?: string;
  icon: string;
}

function StatCard({ title, value, subtitle, icon }: StatCardProps) {
  return (
    <View style={styles.statCardContainer}>
      <Card variant="light" style={styles.statCardStyle}>
        <View style={styles.statHeader}>
          <View style={styles.iconContainer}>
            <Ionicons name={icon as any} size={18} color={tokens.colors.text.secondary} />
          </View>
          <Text style={styles.statTitle}>{title}</Text>
        </View>
        <Text style={styles.statValue}>{value}</Text>
        {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
      </Card>
    </View>
  );
}

export default function ImpactScreen() {
  const [viewType, setViewType] = useState<'global' | 'personal'>('global');

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient />
      <AppTopBar />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          <Card variant="light" style={styles.tabCardStyle} padding={false}>
            <View style={styles.tabWrapper}>
              <TouchableOpacity
                style={[styles.tab, viewType === 'global' && styles.activeTab]}
                onPress={() => setViewType('global')}
                activeOpacity={0.7}
              >
                <Text style={[styles.tabText, viewType === 'global' && styles.activeTabText]}>
                  Global Impact
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.tab, viewType === 'personal' && styles.activeTab]}
                onPress={() => setViewType('personal')}
                activeOpacity={0.7}
              >
                <Text style={[styles.tabText, viewType === 'personal' && styles.activeTabText]}>
                  Personal Impact
                </Text>
              </TouchableOpacity>
            </View>
          </Card>
        </View>

        {/* Content */}
        <View style={styles.contentArea}>
          {viewType === 'global' ? (
            <>
              {/* Hero Stats */}
              <View style={styles.heroStatsContainer}>
                <Card variant="medium" style={styles.heroStatsCardStyle}>
                  <View style={styles.heroMainStat}>
                    <Text style={styles.heroValue}>15.2K</Text>
                    <Text style={styles.heroLabel}>Total Prayers This Month</Text>
                  </View>

                  <View style={styles.subStats}>
                    <View style={styles.subStat}>
                      <Text style={styles.subStatValue}>8.7K</Text>
                      <Text style={styles.subStatLabel}>Active Users</Text>
                    </View>
                    <View style={styles.subStatDivider} />
                    <View style={styles.subStat}>
                      <Text style={styles.subStatValue}>156</Text>
                      <Text style={styles.subStatLabel}>Countries</Text>
                    </View>
                    <View style={styles.subStatDivider} />
                    <View style={styles.subStat}>
                      <Text style={styles.subStatValue}>42.3K</Text>
                      <Text style={styles.subStatLabel}>Lives Touched</Text>
                    </View>
                  </View>
                </Card>
              </View>

              {/* Prayer Outcomes */}
              <View style={styles.outcomesCardContainer}>
                <Card variant="light" style={styles.outcomesCardStyle}>
                  <View style={styles.cardHeader}>
                    <Ionicons
                      name="analytics-outline"
                      size={20}
                      color={tokens.colors.text.secondary}
                    />
                    <Text style={styles.cardTitle}>Prayer Outcomes</Text>
                  </View>
                  <View style={styles.outcomesList}>
                    <View style={styles.outcomeItem}>
                      <View style={styles.outcomeLeft}>
                        <View
                          style={[
                            styles.outcomeDot,
                            { backgroundColor: tokens.colors.success[500] },
                          ]}
                        />
                        <Text style={styles.outcomeLabel}>Answered</Text>
                      </View>
                      <View style={styles.outcomeRight}>
                        <Text style={styles.outcomeValue}>65%</Text>
                        <View
                          style={[
                            styles.outcomeBar,
                            { width: '65%', backgroundColor: tokens.colors.success[500] },
                          ]}
                        />
                      </View>
                    </View>
                    <View style={styles.outcomeItem}>
                      <View style={styles.outcomeLeft}>
                        <View
                          style={[
                            styles.outcomeDot,
                            { backgroundColor: tokens.colors.warning[500] },
                          ]}
                        />
                        <Text style={styles.outcomeLabel}>Still Waiting</Text>
                      </View>
                      <View style={styles.outcomeRight}>
                        <Text style={styles.outcomeValue}>25%</Text>
                        <View
                          style={[
                            styles.outcomeBar,
                            { width: '25%', backgroundColor: tokens.colors.warning[500] },
                          ]}
                        />
                      </View>
                    </View>
                    <View style={styles.outcomeItem}>
                      <View style={styles.outcomeLeft}>
                        <View
                          style={[styles.outcomeDot, { backgroundColor: tokens.colors.error[500] }]}
                        />
                        <Text style={styles.outcomeLabel}>Not Answered</Text>
                      </View>
                      <View style={styles.outcomeRight}>
                        <Text style={styles.outcomeValue}>10%</Text>
                        <View
                          style={[
                            styles.outcomeBar,
                            { width: '10%', backgroundColor: tokens.colors.error[500] },
                          ]}
                        />
                      </View>
                    </View>
                  </View>
                </Card>
              </View>

              {/* Additional Global Stats */}
              <View style={styles.additionalStats}>
                <View style={styles.statsRow}>
                  <View style={styles.statBoxContainer}>
                    <Card variant="light" style={styles.statBoxCardStyle}>
                      <View style={[styles.statBoxIcon, styles.infoIcon]}>
                        <Ionicons name="time-outline" size={20} color={tokens.colors.info[400]} />
                      </View>
                      <Text style={styles.statBoxValue}>24/7</Text>
                      <Text style={styles.statBoxLabel}>Prayer Coverage</Text>
                    </Card>
                  </View>
                  <View style={styles.statBoxContainer}>
                    <Card variant="light" style={styles.statBoxCardStyle}>
                      <View style={[styles.statBoxIcon, styles.successIcon]}>
                        <Ionicons
                          name="language-outline"
                          size={20}
                          color={tokens.colors.success[400]}
                        />
                      </View>
                      <Text style={styles.statBoxValue}>47</Text>
                      <Text style={styles.statBoxLabel}>Languages</Text>
                    </Card>
                  </View>
                </View>
                <View style={styles.statsRow}>
                  <View style={styles.statBoxContainer}>
                    <Card variant="light" style={styles.statBoxCardStyle}>
                      <View style={[styles.statBoxIcon, styles.warningIcon]}>
                        <Ionicons
                          name="flash-outline"
                          size={20}
                          color={tokens.colors.warning[400]}
                        />
                      </View>
                      <Text style={styles.statBoxValue}>98.2%</Text>
                      <Text style={styles.statBoxLabel}>Uptime</Text>
                    </Card>
                  </View>
                  <View style={styles.statBoxContainer}>
                    <Card variant="light" style={styles.statBoxCardStyle}>
                      <View style={[styles.statBoxIcon, styles.prayerIcon]}>
                        <Ionicons
                          name="trending-up-outline"
                          size={20}
                          color={tokens.colors.prayer[400]}
                        />
                      </View>
                      <Text style={styles.statBoxValue}>+23%</Text>
                      <Text style={styles.statBoxLabel}>Growth Rate</Text>
                    </Card>
                  </View>
                </View>
              </View>

              {/* Top Categories */}
              <View style={styles.categoriesCardContainer}>
                <View style={styles.categoriesCard}>
                  <View style={styles.cardHeader}>
                    <Ionicons name="list-outline" size={20} color={tokens.colors.text.secondary} />
                    <Text style={styles.cardTitle}>Top Prayer Categories</Text>
                  </View>
                  <View style={styles.categoriesList}>
                    {[
                      {
                        name: 'Health & Healing',
                        count: 1247,
                        percentage: 32,
                        color: tokens.colors.success[500],
                      },
                      {
                        name: 'World Peace',
                        count: 892,
                        percentage: 23,
                        color: tokens.colors.info[500],
                      },
                      {
                        name: 'Gratitude',
                        count: 645,
                        percentage: 17,
                        color: tokens.colors.prayer[500],
                      },
                      {
                        name: 'Family',
                        count: 521,
                        percentage: 14,
                        color: tokens.colors.warning[500],
                      },
                      {
                        name: 'Abundance',
                        count: 387,
                        percentage: 10,
                        color: tokens.colors.primary[500],
                      },
                      {
                        name: 'Guidance',
                        count: 156,
                        percentage: 4,
                        color: tokens.colors.text.tertiary,
                      },
                    ].map((category, index) => (
                      <View key={index} style={styles.categoryItem}>
                        <View style={styles.categoryLeft}>
                          <View style={[styles.categoryDot, { backgroundColor: category.color }]} />
                          <View style={styles.categoryInfo}>
                            <Text style={styles.categoryName}>{category.name}</Text>
                            <View style={styles.categoryBar}>
                              <View
                                style={[
                                  styles.categoryBarFill,
                                  {
                                    width: `${category.percentage}%`,
                                    backgroundColor: category.color,
                                  },
                                ]}
                              />
                            </View>
                          </View>
                        </View>
                        <View style={styles.categoryRight}>
                          <Text style={styles.categoryCount}>
                            {category.count.toLocaleString()}
                          </Text>
                          <Text style={styles.categoryPercentage}>{category.percentage}%</Text>
                        </View>
                      </View>
                    ))}
                  </View>
                </View>
              </View>

              {/* Global Milestones */}
              <View style={styles.milestonesCardContainer}>
                <Card variant="light" style={styles.milestonesCardStyle}>
                  <View style={styles.cardHeader}>
                    <Ionicons
                      name="trophy-outline"
                      size={20}
                      color={tokens.colors.text.secondary}
                    />
                    <Text style={styles.cardTitle}>Community Milestones</Text>
                  </View>
                  <View style={styles.milestonesList}>
                    <View style={styles.milestoneItem}>
                      <View style={[styles.milestoneIcon, styles.warningIcon]}>
                        <Ionicons name="trophy" size={16} color={tokens.colors.warning[400]} />
                      </View>
                      <Text style={styles.milestoneText}>100K prayers reached this year!</Text>
                    </View>
                    <View style={styles.milestoneItem}>
                      <View style={[styles.milestoneIcon, styles.infoIcon]}>
                        <Ionicons name="globe" size={16} color={tokens.colors.info[400]} />
                      </View>
                      <Text style={styles.milestoneText}>Available in 156 countries</Text>
                    </View>
                    <View style={styles.milestoneItem}>
                      <View style={[styles.milestoneIcon, styles.prayerIcon]}>
                        <Ionicons name="heart" size={16} color={tokens.colors.prayer[400]} />
                      </View>
                      <Text style={styles.milestoneText}>1M+ lives touched by prayer</Text>
                    </View>
                    <View style={styles.milestoneItem}>
                      <View style={[styles.milestoneIcon, styles.successIcon]}>
                        <Ionicons name="people" size={16} color={tokens.colors.success[400]} />
                      </View>
                      <Text style={styles.milestoneText}>50K+ active prayer warriors</Text>
                    </View>
                  </View>
                </Card>
              </View>
            </>
          ) : (
            <>
              {/* Personal Stats Grid */}
              <View style={styles.statsGrid}>
                <StatCard
                  title="Prayers Created"
                  value="12"
                  subtitle="This month: +3"
                  icon="create-outline"
                />
                <StatCard
                  title="Prayers Joined"
                  value="89"
                  subtitle="This month: +23"
                  icon="people-outline"
                />
                <StatCard
                  title="Success Rate"
                  value="67%"
                  subtitle="Above average"
                  icon="checkmark-outline"
                />
                <StatCard
                  title="Lives Touched"
                  value="2.3K"
                  subtitle="This month: +542"
                  icon="heart-outline"
                />
                <StatCard
                  title="Days Active"
                  value="47"
                  subtitle="Current streak: 12"
                  icon="calendar-outline"
                />
                <StatCard
                  title="Comments Made"
                  value="156"
                  subtitle="Encouragement given"
                  icon="chatbubble-outline"
                />
                <StatCard
                  title="Followers"
                  value="234"
                  subtitle="Growing community"
                  icon="person-add-outline"
                />
                <StatCard
                  title="Prayer Time"
                  value="18h"
                  subtitle="Total this month"
                  icon="time-outline"
                />
              </View>

              {/* Personal Insight */}
              <View style={styles.personalInsightContainer}>
                <Card variant="light" style={styles.personalInsightCardStyle}>
                  <View style={styles.insightHeader}>
                    <Ionicons name="heart" size={20} color={tokens.colors.prayer[400]} />
                    <Text style={styles.insightTitle}>Your Impact This Month</Text>
                  </View>
                  <Text style={styles.insightText}>
                    Your prayers have reached{' '}
                    <Text style={styles.insightHighlight}>2,341 people</Text> across{' '}
                    <Text style={styles.insightHighlight}>23 countries</Text>. Keep spreading hope!
                    🙏
                  </Text>
                </Card>
              </View>

              {/* Recent Activity */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Recent Activity</Text>
                <Card variant="light" style={styles.activityCardStyle}>
                  <View style={styles.activityList}>
                    <View style={styles.activityItem}>
                      <View style={[styles.activityDot, { backgroundColor: '#10b981' }]} />
                      <Text style={styles.activityText}>Prayer for healing answered</Text>
                      <Text style={styles.activityTime}>2 days ago</Text>
                    </View>
                    <View style={styles.activityItem}>
                      <View style={[styles.activityDot, { backgroundColor: '#3b82f6' }]} />
                      <Text style={styles.activityText}>Joined community prayer</Text>
                      <Text style={styles.activityTime}>3 days ago</Text>
                    </View>
                    <View style={styles.activityItem}>
                      <View style={[styles.activityDot, { backgroundColor: '#eab308' }]} />
                      <Text style={styles.activityText}>Created gratitude prayer</Text>
                      <Text style={styles.activityTime}>1 week ago</Text>
                    </View>
                  </View>
                </Card>
              </View>
            </>
          )}
        </View>

        {/* Bottom padding */}
        <View style={{ height: 100 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - proper header spacing
  },
  tabContainer: {
    paddingHorizontal: tokens.spacing.md, // 16px - consistent with other screens
    paddingVertical: tokens.spacing.lg, // 24px
  },
  tabCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  tabWrapper: {
    flexDirection: 'row',
    padding: tokens.spacing.xs, // 4px
    overflow: 'hidden',
  },
  tab: {
    flex: 1,
    paddingVertical: tokens.spacing.md,
    paddingHorizontal: tokens.spacing.md,
    borderRadius: 2,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: tokens.colors.surface.glassMedium,
  },
  tabText: {
    ...textStyles.buttonTextSmall,
    color: tokens.colors.text.tertiary,
  },
  activeTabText: {
    color: tokens.colors.text.primary,
  },
  contentArea: {
    paddingHorizontal: tokens.spacing.md, // 16px - consistent with other screens
  },

  // Hero Stats
  heroStatsContainer: {
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
    marginBottom: tokens.spacing['2xl'],
  },
  heroStatsCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  heroStatsContent: {
    alignItems: 'center',
  },
  heroMainStat: {
    alignItems: 'center',
    marginBottom: tokens.spacing['2xl'],
  },
  heroValue: {
    ...tokens.typography.display.large,
    color: tokens.colors.text.primary,
    letterSpacing: -1,
    marginBottom: tokens.spacing.sm,
  },
  heroLabel: {
    ...tokens.typography.body.large,
    color: tokens.colors.text.tertiary,
  },
  subStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.lg,
  },
  subStat: {
    alignItems: 'center',
  },
  subStatDivider: {
    width: 1,
    height: 32,
    backgroundColor: tokens.colors.surface.glassBorder,
  },
  subStatValue: {
    ...tokens.typography.heading.h3,
    color: tokens.colors.text.primary,
  },
  subStatLabel: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
    marginTop: tokens.spacing.xs, // 4px
  },

  // Card Headers
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.lg,
  },
  cardTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    color: tokens.colors.text.primary,
  },

  // Prayer Outcomes
  outcomesCardContainer: {
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
    marginBottom: tokens.spacing['2xl'],
  },
  outcomesCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  outcomesList: {
    gap: tokens.spacing.md,
  },
  outcomeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: tokens.spacing.sm,
  },
  outcomeLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.md,
    flex: 1,
  },
  outcomeRight: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  outcomeDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  outcomeLabel: {
    ...textStyles.body,
    color: tokens.colors.text.secondary,
  },
  outcomeValue: {
    ...tokens.typography.label.large,
    color: tokens.colors.text.primary,
    fontFamily: 'Roboto-Regular',
    marginBottom: tokens.spacing.xs, // 4px
  },
  outcomeBar: {
    height: 3,
    borderRadius: 2,
    minWidth: 20,
  },

  // Stats Grid
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing['2xl'],
  },
  statCardContainer: {
    width: '47%',
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
  },
  statCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.md,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: tokens.colors.surface.glassMedium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statTitle: {
    ...textStyles.buttonTextSmall,
    color: tokens.colors.text.secondary,
    flex: 1,
  },
  statValue: {
    ...textStyles.h2,
    color: tokens.colors.text.primary,
    letterSpacing: -0.5,
    marginBottom: tokens.spacing.sm,
  },
  statSubtitle: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
  },

  // Additional Stats
  additionalStats: {
    marginBottom: tokens.spacing['2xl'],
  },
  statsRow: {
    flexDirection: 'row',
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.md,
  },
  statBoxContainer: {
    flex: 1,
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
  },
  statBoxCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  statBoxContent: {
    alignItems: 'center',
    gap: tokens.spacing.sm,
  },
  statBoxIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoIcon: {
    backgroundColor: tokens.colors.info[100],
  },
  successIcon: {
    backgroundColor: tokens.colors.success[100],
  },
  warningIcon: {
    backgroundColor: tokens.colors.warning[100],
  },
  prayerIcon: {
    backgroundColor: tokens.colors.prayer[100],
  },
  statBoxValue: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    color: tokens.colors.text.primary,
  },
  statBoxLabel: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
    textAlign: 'center',
  },

  // Categories
  categoriesCardContainer: {
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
    marginBottom: tokens.spacing['2xl'],
  },
  categoriesCard: {
    backgroundColor: tokens.colors.surface.glassLight,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    padding: tokens.spacing.lg,
  },
  categoriesList: {
    gap: tokens.spacing.md,
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.md,
    flex: 1,
  },
  categoryDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    ...textStyles.body,
    color: tokens.colors.text.secondary,
    marginBottom: tokens.spacing.xs, // 4px
  },
  categoryBar: {
    height: 3,
    backgroundColor: tokens.colors.surface.glassMedium,
    borderRadius: 2,
    overflow: 'hidden',
  },
  categoryBarFill: {
    height: '100%',
    borderRadius: 2,
  },
  categoryRight: {
    alignItems: 'flex-end',
    minWidth: 60,
  },
  categoryCount: {
    ...textStyles.buttonTextSmall,
    color: tokens.colors.text.tertiary,
  },
  categoryPercentage: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.quaternary,
    marginTop: tokens.spacing.xs, // 4px - better than 2px
  },

  // Milestones
  milestonesCardContainer: {
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
    marginBottom: tokens.spacing['2xl'],
  },
  milestonesCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  milestonesList: {
    gap: tokens.spacing.md,
  },
  milestoneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.md,
  },
  milestoneIcon: {
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
  },
  milestoneText: {
    ...textStyles.body,
    color: tokens.colors.text.secondary,
    flex: 1,
  },

  // Personal Insight
  personalInsightContainer: {
    borderRadius: tokens.radius.sm,
    overflow: 'hidden',
    marginBottom: tokens.spacing['2xl'],
  },
  personalInsightCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  personalInsightContent: {
    alignItems: 'center',
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.md,
  },
  insightTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    color: tokens.colors.text.primary,
  },
  insightText: {
    ...textStyles.body,
    color: tokens.colors.text.tertiary,
    textAlign: 'center',
    lineHeight: 24,
  },
  insightHighlight: {
    color: tokens.colors.text.primary,
    fontFamily: 'Roboto-Regular',
  },

  // Activity
  section: {
    marginBottom: tokens.spacing['2xl'],
  },
  sectionTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    color: tokens.colors.text.primary,
    marginBottom: tokens.spacing.lg,
  },
  activityCardStyle: {
    borderRadius: tokens.radius.sm,
  },
  activityList: {
    gap: tokens.spacing.md,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
  },
  activityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  activityText: {
    flex: 1,
    ...textStyles.body,
    color: tokens.colors.text.secondary,
  },
  activityTime: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
  },
});

// Removed duplicate export
