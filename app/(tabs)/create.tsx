import { View, Text, TouchableOpacity, ScrollView, TextInput, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { spacing, radius, surfaces, withOpacity } from '../../lib/design';
import { tokens } from '../../lib/design/tokens';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Chip from '../../components/ui/Chip';
import GradientBackground from '../../components/GradientBackground';
import AppTopBar from '../../components/AppTopBar';
import { useToast } from '../../hooks/useToast';
// Removed BlurView import
import { useAuth } from '../../contexts/AuthContext';
import { supabase, PrayerInsert } from '../../lib/supabase';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Ionicons from '@expo/vector-icons/Ionicons';
import { formatScheduledDate, formatScheduledTime } from '../../utils/dateUtils';
// Remove duplicate import

export default function CreatePrayerScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const toast = useToast();
  // Remove old tokens usage
  const [loading, setLoading] = useState(false);
  const [isPickerVisible, setPickerVisibility] = useState(false);
  const [pickerMode, setPickerMode] = useState<'date' | 'time'>('date');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [durationMinutes, setDurationMinutes] = useState<number | null>(1440); // default 24h or null for ongoing
  const [formData, setFormData] = useState({
    title: '',
    prayerText: '',
    description: '',
    category: 'Health & Healing',
    allowsBroadcast: false,
    isScheduled: false,
  });

  // Categories from impact screen
  const categories = [
    'Health & Healing',
    'World Peace',
    'Gratitude',
    'Family',
    'Abundance',
    'Guidance',
  ];

  const showDatePicker = () => {
    setPickerMode('date');
    setPickerVisibility(true);
  };

  const showTimePicker = () => {
    setPickerMode('time');
    setPickerVisibility(true);
  };

  const hidePicker = () => {
    setPickerVisibility(false);
  };

  const handleConfirm = (date: Date) => {
    // Ensure we have a valid date
    if (!date || isNaN(date.getTime())) {
      console.warn('Invalid date received in handleConfirm:', date);
      hidePicker();
      return;
    }

    if (pickerMode === 'date') {
      setSelectedDate(date);
    } else {
      setSelectedTime(date);
    }
    hidePicker();
  };



  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.showErrorToast('Title Required', 'Please enter a title for your prayer');
      return false;
    }

    if (!formData.description.trim()) {
      toast.showErrorToast('Description Required', 'Please enter a description for your prayer');
      return false;
    }

    if (!formData.prayerText.trim()) {
      toast.showErrorToast('Prayer Text Required', 'Please write your prayer text');
      return false;
    }

    if (!user) {
      toast.showErrorToast('Authentication Error', 'Please sign in to create a prayer');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      // Combine date and time if scheduled
      let scheduledDate = null;
      if (formData.isScheduled) {
        if (
          !selectedDate ||
          isNaN(selectedDate.getTime()) ||
          !selectedTime ||
          isNaN(selectedTime.getTime())
        ) {
          toast.showErrorToast('Invalid Date/Time', 'Please select a valid date and time');
          return;
        }

        const combined = new Date(selectedDate);
        combined.setHours(selectedTime.getHours());
        combined.setMinutes(selectedTime.getMinutes());

        if (isNaN(combined.getTime())) {
          toast.showErrorToast('Invalid Date/Time', 'Please select a valid date and time');
          return;
        }

        scheduledDate = combined.toISOString();
      }

      const prayerData: PrayerInsert = {
        user_id: user!.id,
        title: formData.title.trim(),
        description: formData.description.trim(),
        prayer_text: formData.prayerText.trim() || null,
        category: formData.category,
        allows_broadcast: formData.allowsBroadcast,
        is_anonymous: false,
        is_answered: false,
        is_live: formData.allowsBroadcast && !formData.isScheduled,
      };

      // Include schedule fields if present (cast to any until types are regenerated)
      const insertData: any = { ...prayerData };
      if (formData.isScheduled && scheduledDate) {
        insertData.scheduled_at = scheduledDate;
        insertData.duration_minutes = durationMinutes;
        insertData.is_live = false;
      }

      const { data: _data, error } = await supabase
        .from('prayers')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error creating prayer:', error);
        toast.showErrorToast('Failed to Create Prayer', error.message);
      } else {
        toast.showPrayerToast(
          'Prayer Created Successfully!',
          `"${formData.title}" has been shared with the community`,
        );

        // Reset form
        setFormData({
          title: '',
          prayerText: '',
          description: '',
          category: 'Health & Healing',
          allowsBroadcast: false,
          isScheduled: false,
        });
        setSelectedDate(new Date());
        setSelectedTime(new Date());

        // Navigate back to home/profile
        router.back();
      }
    } catch (error) {
      console.error('Error creating prayer:', error);
      toast.showErrorToast('Failed to Create Prayer', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Create Prayer</Text>
          <Text style={styles.subtitle}>
            Share your prayer with the community and invite others to join you in faith
          </Text>
        </View>
        {/* Form */}
        <View style={styles.form}>
          {/* Prayer Title - Card Style with Label Inside */}
          <View style={styles.fieldCardContainer}>
            <Card>
              <Text style={styles.fieldLabel}>
                Prayer Title <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                value={formData.title}
                onChangeText={(text) => setFormData({ ...formData, title: text })}
                placeholder="Brief, meaningful title..."
                placeholderTextColor={surfaces.text.tertiary}
                style={styles.transparentInput}
              />
              <Text style={styles.fieldDescription}>Brief, meaningful title for your prayer</Text>
            </Card>
          </View>

          {/* Description - Card Style with Label Inside */}
          <View style={styles.fieldCardContainer}>
            <Card>
              <Text style={styles.fieldLabel}>
                Description <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                value={formData.description}
                onChangeText={(text) => setFormData({ ...formData, description: text })}
                placeholder="Briefly describe what this prayer is about..."
                placeholderTextColor={surfaces.text.tertiary}
                style={[styles.transparentInput, styles.textArea]}
                multiline
                numberOfLines={3}
              />
              <Text style={styles.fieldDescription}>
                Help others understand what this prayer is about
              </Text>
            </Card>
          </View>

          {/* Prayer Text - Card Style with Label Inside */}
          <View style={styles.fieldCardContainer}>
            <Card>
              <Text style={styles.fieldLabel}>
                Your Prayer <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                value={formData.prayerText}
                onChangeText={(text) => setFormData({ ...formData, prayerText: text })}
                placeholder="Write your prayer here. Share your heart with the community..."
                placeholderTextColor={surfaces.text.tertiary}
                style={[styles.transparentInput, styles.largeTextArea]}
                multiline
                numberOfLines={6}
              />
              <Text style={styles.fieldDescription}>
                Share your heartfelt prayer with the community
              </Text>
            </Card>
          </View>

          {/* Category - Custom Selector */}
          <View style={styles.fieldCardContainer}>
            <Card>
              <Text style={styles.simpleLabel}>
                Category <Text style={styles.required}>*</Text>
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categoryScroll}
              >
                {categories.map((category) => (
                  <Chip
                    key={category}
                    label={category}
                    selected={formData.category === category}
                    onPress={() => setFormData({ ...formData, category })}
                  />
                ))}
              </ScrollView>
            </Card>
          </View>

          {/* Schedule Prayer */}
          <View style={styles.fieldCardContainer}>
            <View style={styles.fieldCard}>
              <View style={styles.broadcastContent}>
                <View style={{ flex: 1 }}>
                  <Text style={styles.broadcastLabel}>Schedule Prayer</Text>
                  <Text style={styles.broadcastDescription}>
                    Schedule this prayer for a specific date and time, or pray now.
                  </Text>
                </View>
                <View style={{ flexDirection: 'row', gap: spacing.md }}>
                  <TouchableOpacity
                    onPress={() => setFormData({ ...formData, isScheduled: false })}
                    style={{
                      flex: 1,
                      backgroundColor: formData.isScheduled
                        ? 'rgba(255,255,255,0.05)'
                        : 'rgba(99, 102, 241, 0.2)',
                      borderWidth: 1,
                      borderColor: formData.isScheduled
                        ? 'rgba(255,255,255,0.1)'
                        : 'rgba(99, 102, 241, 0.4)',
                      borderRadius: spacing.sm,
                      padding: spacing.md,
                      alignItems: 'center',
                    }}
                    activeOpacity={0.8}
                  >
                    <Text style={{ color: 'white', fontWeight: '600' }}>Start Now</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    onPress={() => setFormData({ ...formData, isScheduled: true })}
                    style={{
                      flex: 1,
                      backgroundColor: formData.isScheduled
                        ? 'rgba(99, 102, 241, 0.2)'
                        : 'rgba(255,255,255,0.05)',
                      borderWidth: 1,
                      borderColor: formData.isScheduled
                        ? 'rgba(99, 102, 241, 0.4)'
                        : 'rgba(255,255,255,0.1)',
                      borderRadius: spacing.sm,
                      padding: spacing.md,
                      alignItems: 'center',
                    }}
                    activeOpacity={0.8}
                  >
                    <Text style={{ color: 'white', fontWeight: '600' }}>Schedule Later</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            {/* Duration chips when scheduled */}
            {formData.isScheduled && (
              <View style={styles.fieldCardContainer}>
                <View style={styles.fieldCard}>
                  <Text style={styles.simpleLabel}>Duration</Text>
                  <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: spacing.sm }}>
                    {[
                      { label: '24 Hours', value: 1440 },
                      { label: '3 Days', value: 4320 },
                      { label: '1 Week', value: 10080 },
                      { label: 'Ongoing', value: null },
                    ].map((opt) => (
                      <TouchableOpacity
                        key={String(opt.value)}
                        onPress={() => setDurationMinutes(opt.value)}
                        style={{
                          backgroundColor:
                            durationMinutes === opt.value
                              ? 'rgba(99, 102, 241, 0.3)'
                              : 'rgba(255,255,255,0.1)',
                          borderWidth: 1,
                          borderColor:
                            durationMinutes === opt.value
                              ? 'rgba(99, 102, 241, 0.6)'
                              : 'rgba(255,255,255,0.2)',
                          borderRadius: radius.sm,
                          paddingHorizontal: spacing.lg,
                          paddingVertical: spacing.sm,
                        }}
                        activeOpacity={0.8}
                      >
                        <Text
                          style={{
                            color: 'white',
                            fontSize: 14,
                            fontWeight: durationMinutes === opt.value ? '600' : '400',
                          }}
                        >
                          {opt.label}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>
              </View>
            )}
          </View>

          {/* Date and Time Pickers - Show only when scheduled */}
          {formData.isScheduled && (
            <View style={styles.fieldCardContainer}>
              <View style={styles.fieldCard}>
                <Text style={styles.simpleLabel}>Schedule Date & Time</Text>
                <View style={styles.dateTimeRow}>
                  <TouchableOpacity style={styles.dateTimeHalfButton} onPress={showDatePicker}>
                    <Ionicons name="calendar-outline" size={20} color="rgba(255,255,255,0.7)" />
                    <Text style={styles.dateTimeText}>{formatScheduledDate(selectedDate)}</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.dateTimeHalfButton} onPress={showTimePicker}>
                    <Ionicons name="time-outline" size={20} color="rgba(255,255,255,0.7)" />
                    <Text style={styles.dateTimeText}>{formatScheduledTime(selectedTime)}</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}

          {/* Broadcast Option */}
          <View style={styles.fieldCardContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.simpleLabel}>Live Broadcasting</Text>
              <Text style={styles.fieldDescription}>
                Enable this to broadcast your prayer live and let others join you in real-time.
              </Text>
              <TouchableOpacity
                style={[
                  styles.broadcastToggleButton,
                  formData.allowsBroadcast && styles.broadcastToggleButtonActive,
                ]}
                onPress={() =>
                  setFormData({ ...formData, allowsBroadcast: !formData.allowsBroadcast })
                }
              >
                <Ionicons
                  name={formData.allowsBroadcast ? 'radio' : 'radio-outline'}
                  size={20}
                  color={formData.allowsBroadcast ? 'white' : 'rgba(255,255,255,0.7)'}
                />
                <Text
                  style={[
                    styles.broadcastToggleText,
                    formData.allowsBroadcast && styles.broadcastToggleTextActive,
                  ]}
                >
                  {formData.allowsBroadcast
                    ? 'Live Broadcasting Enabled'
                    : 'Enable Live Broadcasting'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Submit Button */}
          <View style={styles.submitButtonContainer}>
            <Button
              title={loading ? 'Creating Prayer...' : 'Create Prayer'}
              onPress={handleSubmit}
              disabled={loading}
              loading={loading}
              variant="primary"
              fullWidth
            />
          </View>
        </View>
        {/* Bottom padding - Account for tab bar */}
        <View style={{ height: spacing.lg }} /> {/* 24px bottom padding */}
      </ScrollView>

      <DateTimePickerModal
        isVisible={isPickerVisible}
        mode={pickerMode}
        onConfirm={handleConfirm}
        onCancel={hidePicker}
        date={pickerMode === 'date' ? selectedDate : selectedTime}
        minimumDate={pickerMode === 'date' ? new Date() : undefined}
        is24Hour={false}
        isDarkModeEnabled={true}
        confirmTextIOS="Confirm"
        cancelTextIOS="Cancel"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - consistent with other screens
  },
  scrollContainer: {
    paddingBottom: tokens.spacing['6xl'], // 96px - consistent bottom spacing
  },
  header: {
    alignItems: 'center',
    paddingHorizontal: spacing.md, // 16px - standardized container padding
    marginBottom: spacing.xl, // 32px
    paddingTop: spacing.sm, // 8px
  },
  title: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    marginBottom: spacing.md, // 16px - better text spacing
    letterSpacing: -0.5,
    fontFamily: 'Roboto-Light',
  },
  subtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7),
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
  },
  form: {
    paddingHorizontal: spacing.md, // 16px - consistent content padding
  },
  // Card style fields with label inside
  fieldCardContainer: {
    marginBottom: spacing.xl,
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  fieldCard: {
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.1),
    padding: spacing.lg, // 24px
  },
  fieldLabel: {
    fontSize: 18,
    fontWeight: '500',
    color: surfaces.text.primary,
    marginBottom: spacing.md, // 16px - better label spacing
    fontFamily: 'Roboto-Regular',
  },
  required: {
    color: '#f87171',
  },
  transparentInput: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: surfaces.text.primary,
    fontSize: 16,
    padding: 0,
    fontFamily: 'Roboto-Regular',
  },
  regularInput: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: surfaces.text.primary,
    fontSize: 16,
    paddingVertical: spacing.sm,
    fontFamily: 'Roboto-Regular',
  },
  fieldDescription: {
    fontSize: 14,
    color: withOpacity(surfaces.text.primary, 0.6),
    marginTop: spacing.md, // 16px - better description spacing
    lineHeight: spacing.lg, // 24px - better line height
    fontFamily: 'Roboto-Regular',
  },
  textArea: {
    minHeight: tokens.spacing['4xl'], // 64px - 8px aligned
    textAlignVertical: 'top',
  },
  largeTextArea: {
    minHeight: 176, // 22 * 8px = 176px - 8px aligned
    textAlignVertical: 'top',
  },
  // Simple fields without cards
  simpleField: {
    marginBottom: spacing.lg, // 24px
  },
  simpleLabel: {
    fontSize: 18,
    fontWeight: '500',
    color: surfaces.text.primary,
    marginBottom: spacing.md, // 16px
    fontFamily: 'Roboto-Regular',
  },
  borderedInput: {
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    color: surfaces.text.primary,
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
  },
  pickerContainer: {
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  // Removed duplicate picker style - using the one at bottom
  pickerItem: {
    color: surfaces.text.primary,
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
  },
  row: {
    flexDirection: 'row',
  },
  broadcastContainer: {
    backgroundColor: surfaces.glass.subtle,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.1),
    padding: spacing.lg, // 24px - better than 20px
  },
  broadcastContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.lg,
  },
  broadcastLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: surfaces.text.primary,
    marginBottom: spacing.sm, // 8px
    fontFamily: 'Roboto-Regular',
  },
  broadcastDescription: {
    fontSize: 14,
    color: withOpacity(surfaces.text.primary, 0.6),
    lineHeight: spacing.lg, // 24px - better line height
    fontFamily: 'Roboto-Regular',
  },
  submitButtonContainer: {
    marginTop: spacing.sm,
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  submitButton: {
    backgroundColor: surfaces.glass.medium,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.3),
  },
  submitButtonInner: {
    paddingVertical: spacing.lg,
    alignItems: 'center',
    width: '100%',
  },
  submitButtonText: {
    fontSize: 18,
    fontWeight: '500',
    color: surfaces.text.primary,
    fontFamily: 'Roboto-Regular',
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  categoryScroll: {
    marginTop: spacing.md, // 16px
  },
  categoryChip: {
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    marginRight: spacing.md,
  },
  categoryChipSelected: {
    backgroundColor: 'rgba(99, 102, 241, 0.3)',
    borderColor: 'rgba(99, 102, 241, 0.6)',
  },
  categoryChipText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    fontFamily: 'Roboto-Regular',
  },
  categoryChipTextSelected: {
    color: 'white',
    fontWeight: '500',
  },
  dateTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    borderRadius: spacing.sm, // 8px - aligned to system
    padding: spacing.md, // 16px - better than 12px
    marginTop: spacing.sm, // 8px
    gap: spacing.sm, // 8px - aligned
  },
  dateTimeText: {
    fontSize: 16,
    color: 'white',
    fontFamily: 'Roboto-Regular',
  },
  dateTimeRow: {
    flexDirection: 'row',
    gap: spacing.md, // 16px - better button spacing
    marginTop: spacing.sm, // 8px
  },
  dateTimeHalfButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    borderRadius: radius.sm,
    padding: spacing.md,
    gap: spacing.sm, // 8px - aligned
  },
  broadcastToggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    borderRadius: radius.sm,
    padding: spacing.lg,
    marginTop: spacing.md,
    gap: spacing.md,
  },
  broadcastToggleButtonActive: {
    backgroundColor: 'rgba(99, 102, 241, 0.2)',
    borderColor: 'rgba(99, 102, 241, 0.4)',
  },
  broadcastToggleText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
    fontFamily: 'Roboto-Regular',
  },
  broadcastToggleTextActive: {
    color: 'white',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  pickerModal: {
    backgroundColor: 'rgba(20,20,20,0.95)',
    borderTopLeftRadius: radius.sm,
    borderTopRightRadius: radius.sm,
    paddingBottom: 34, // Safe area for home indicator
  },
  pickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.md, // 16px
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  pickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  doneButton: {
    backgroundColor: 'rgba(99, 102, 241, 0.8)',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: radius.sm,
  },
  doneButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  picker: {
    height: 200, // Keep as is - picker height
  },
});
