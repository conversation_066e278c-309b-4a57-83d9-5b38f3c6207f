import { View, Text, TouchableOpacity } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { LinearGradient } from 'expo-linear-gradient';
import {
  componentStyles,
  textStyles,
  spacing,
  surfaces,
  withOpacity,
} from '../../lib/design';

import Card from '../../components/ui/Card';
import ElegantSpinner from '../../components/ui/ElegantSpinner';
import { useRouter, useFocusEffect } from 'expo-router';
import GradientBackground from '../../components/GradientBackground';
import AppTopBar from '../../components/AppTopBar';
import { useState, useEffect, useCallback } from 'react';
import { usePremium } from '../../hooks/usePremium';
import PremiumUpgrade from '../../components/PremiumUpgrade';
import ShareModal from '../../components/modals/ShareModal';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, {
  FadeInUp,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { useAuth } from '../../contexts/AuthContext';
import { usePrayerContext } from '../../contexts/PrayerContext';
import { supabase, PrayerWithProfile } from '../../lib/supabase';
import { useToast } from '../../hooks/useToast';
import CommentCount from '../../components/CommentCount';
import { formatRelativeTime } from '../../utils/dateUtils';

import ProfileImage from '../../components/ProfileImage';
import VideoLoadingScreen from '../../components/VideoLoadingScreen';
import Badge from '../../components/ui/Badge';
import PrayerFeedHeader from '../../components/PrayerFeedHeader';
import CollapsibleFilters from '../../components/CollapsibleFilters';
import FloatingActionButton from '../../components/FloatingActionButton';

// Prayer type is imported from supabase as PrayerWithProfile

function PrayerCard({
  prayer,
  onPress,
  index,
  router,
}: {
  prayer: PrayerWithProfile;
  onPress: () => void;
  index: number;
  router: any;
}) {
  const scale = useSharedValue(1);
  const { user } = useAuth();
  // Remove old tokens usage



  // Get user display name and initial
  const getUserDisplayName = () => {
    return (
      prayer.user_profiles?.display_name || prayer.user_profiles?.full_name || 'Anonymous User'
    );
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.96, {
      damping: 15,
      stiffness: 300,
    });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, {
      damping: 15,
      stiffness: 300,
    });
  };

  return (
    <Animated.View entering={FadeInUp.delay(index * 100).duration(500)}>
      <Animated.View style={[animatedStyle]}>
        <TouchableOpacity
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={{
            borderRadius: 12,
            marginBottom: spacing.lg,
          }}
          activeOpacity={0.9}
        >
          <Card style={{ padding: spacing.lg }} padding={false}>

            {/* Content */}
            <View style={{ marginBottom: spacing.lg }}>
              <Text style={{
                color: 'white',
                fontSize: 18,
                fontFamily: 'Roboto-Regular',
                marginBottom: spacing.sm,
                lineHeight: 26,
                letterSpacing: -0.5,
              }}>{prayer.title}</Text>
              <Text style={{
                color: 'rgba(255,255,255,0.75)',
                fontSize: 15,
                lineHeight: 22,
                fontFamily: 'Roboto-Regular',
              }} numberOfLines={3}>
                {prayer.description}
              </Text>
            </View>

            {/* User info below content */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: spacing.md,
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: spacing.sm, flex: 1 }}>
                <ProfileImage
                  avatarUrl={prayer.user_profiles?.avatar_url}
                  displayName={getUserDisplayName()}
                  fullName={prayer.user_profiles?.full_name}
                  size={40}
                />
                <View style={{ flex: 1 }}>
                  <TouchableOpacity
                    onPress={() => {
                      if (user?.id === prayer.user_id) {
                        router.push('/(tabs)/profile');
                      } else {
                        router.push({
                          pathname: '/user-profile',
                          params: { userId: prayer.user_id },
                        });
                      }
                    }}
                  >
                    <Text style={{
                      fontSize: 15,
                      fontFamily: 'Roboto-Regular',
                      color: 'white',
                    }}>
                      {getUserDisplayName()}
                    </Text>
                  </TouchableOpacity>
                  <Text style={{
                    color: 'rgba(255,255,255,0.65)',
                    fontSize: 12,
                    fontFamily: 'Roboto-Regular',
                    marginTop: 2,
                  }}>
                    {formatRelativeTime(prayer.created_at)}
                  </Text>
                </View>
              </View>
              <View style={{ alignItems: 'flex-end', gap: spacing.xs }}>
                {prayer.is_live && <Badge text="LIVE" variant="live" />}
                {prayer.is_answered && <Badge text="ANSWERED" variant="success" />}
              </View>
            </View>

            {/* Stats */}
            <View style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingTop: 16,
              borderTopWidth: 1,
              borderTopColor: 'rgba(255,255,255,0.08)',
            }}>
              <View style={{ flexDirection: 'row', gap: spacing.sm }}>
                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: spacing.sm,
                  paddingHorizontal: 12,
                  paddingVertical: 8,
                  borderRadius: 8,
                  backgroundColor: surfaces.glass.subtle,
                  borderWidth: 1,
                  borderColor: 'rgba(255,255,255,0.06)',
                  overflow: 'hidden',
                }}>
                  <CommentCount
                    prayerId={prayer.id}
                    initialCount={prayer.comment_count || 0}
                    iconColor="rgba(255,255,255,0.7)"
                    iconSize={18}
                    textStyle={{
                      color: 'rgba(255,255,255,0.8)',
                      fontSize: 13,
                      fontFamily: 'Roboto-Regular',
                    }}
                  />
                </View>

                <View style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  gap: spacing.sm,
                  paddingHorizontal: 12,
                  paddingVertical: 8,
                  borderRadius: 8,
                  backgroundColor: surfaces.glass.subtle,
                  borderWidth: 1,
                  borderColor: 'rgba(255,255,255,0.06)',
                  overflow: 'hidden',
                }}>
                  <Ionicons name="people" size={18} color="rgba(255,255,255,0.7)" />
                  <Text style={{
                    color: 'rgba(255,255,255,0.8)',
                    fontSize: 13,
                    fontFamily: 'Roboto-Regular',
                  }}>{prayer.joined_count || 0}</Text>
                </View>
              </View>

              <View style={{ alignItems: 'flex-end' }}>
                <Badge text={prayer.category} variant="category" />
              </View>
            </View>
          </Card>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
}

export default function Home() {
  const router = useRouter();
  const { user } = useAuth();
  const toast = useToast();
  const { setPrayers: setGlobalPrayers } = usePrayerContext();
  // Remove old tokens usage

  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [prayers, setPrayers] = useState<PrayerWithProfile[]>([]);
  const [filteredPrayers, setFilteredPrayers] = useState<PrayerWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [activeFilter, setActiveFilter] = useState<'all' | 'live' | 'week' | 'recent'>('all');
  const [activeCategory, setActiveCategory] = useState<string>('all');


  const ITEMS_PER_PAGE = 20;
  const {
    showUpgradeModal,
    upgradeReason,
    // triggerUpgrade,
    closeUpgradeModal,
    upgradeToPremium,
  } = usePremium();

  // Apply filters when dependencies change
  useEffect(() => {
    let filtered = [...prayers];


    // Apply category filter
    if (activeCategory !== 'all') {
      filtered = filtered.filter((prayer) => prayer.category === activeCategory);
    }

    // Apply time/status filters
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    switch (activeFilter) {
      case 'live':
        filtered = filtered.filter((prayer) => prayer.is_live);
        break;
      case 'week':
        filtered = filtered.filter(
          (prayer) => prayer.created_at && new Date(prayer.created_at) >= oneWeekAgo,
        );
        break;
      case 'recent':
        filtered = filtered.sort((a, b) => {
          const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
          const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
          return dateB - dateA;
        });
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    setFilteredPrayers(filtered);
  }, [prayers, activeCategory, activeFilter]);

  const categories = [
    'all',
    'Health & Healing',
    'Family & Relationships',
    'Work & Career',
    'Spiritual Growth',
    'Gratitude & Praise',
    'Guidance & Wisdom',
    'Other',
  ];
  // Fetch prayers from database with pagination
  const fetchPrayers = useCallback(async (reset = false) => {
    try {
      const currentPrayers = reset ? [] : prayers;
      const from = reset ? 0 : currentPrayers.length;
      const to = from + ITEMS_PER_PAGE - 1;

      const { data: prayersData, error: prayersError } = await supabase
        .from('prayers')
        .select(
          `
          *,
          user_profiles(*)
        `,
        )
        .order('created_at', { ascending: false })
        .range(from, to);

      if (prayersError) {
        console.error('Error fetching prayers:', prayersError);
        toast.showErrorToast('Error loading prayers', prayersError.message);
        return;
      }

      const newPrayers = (prayersData as unknown as PrayerWithProfile[]) || [];
      const updatedPrayers = reset ? newPrayers : [...currentPrayers, ...newPrayers];

      setPrayers(updatedPrayers);
      setGlobalPrayers(updatedPrayers);
      setHasMore(newPrayers.length === ITEMS_PER_PAGE);
    } catch (error) {
      console.error('Error fetching prayers:', error);
      toast.showErrorToast('Error loading prayers', 'Something went wrong');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [toast, setGlobalPrayers, prayers, ITEMS_PER_PAGE]);

  // Load more prayers for pagination
  const loadMorePrayers = useCallback(async () => {
    if (loadingMore || !hasMore) return;

    setLoadingMore(true);
    await fetchPrayers(false);
  }, [loadingMore, hasMore, fetchPrayers]);

  useEffect(() => {
    fetchPrayers(true);
  }, []);

  // Refresh prayers when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // Only refresh if we're not already loading
      if (!loading) {
        fetchPrayers(true);
      }
    }, [loading, fetchPrayers]),
  );

  // Real-time subscription for prayer updates
  useEffect(() => {
    if (!user) return;

    console.log('Setting up realtime subscription for prayers feed...');

    const prayersChannel = supabase
      .channel('prayers-feed', {
        config: {
          broadcast: { self: false },
        },
      })

      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prayer_comments',
        },
        (payload) => {
          console.log('🔄 Prayer comment change in feed:', payload);
          // Refresh prayers when comments change
          setTimeout(() => fetchPrayers(true), 100);
        },
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prayers',
        },
        (payload) => {
          console.log('🔄 Prayer change in feed:', payload);
          // Refresh prayers when new prayers are added
          setTimeout(() => fetchPrayers(true), 100);
        },
      )
      .subscribe((status) => {
        console.log('🔌 Prayers feed subscription status:', status);
      });

    return () => {
      console.log('🔌 Removing prayers feed subscription...');
      supabase.removeChannel(prayersChannel);
    };
  }, [user]);

  // Mock prayers data removed - now using real data from database

  // Render prayer card for FlashList
  const renderPrayerCard = useCallback(({ item, index }: { item: PrayerWithProfile; index: number }) => (
    <PrayerCard
      key={item.id}
      prayer={item}
      index={index}
      router={router}
      onPress={() => {
        console.log(
          '🔍 [HOME FEED] Navigating to prayer detail with ID:',
          item.id,
          'Type:',
          typeof item.id,
        );
        console.log(
          '🔍 [HOME FEED] Full prayer object:',
          JSON.stringify(item, null, 2),
        );
        router.push(`/prayer-detail?id=${item.id}`);
      }}
    />
  ), [router]);

  return (
    <View style={[componentStyles.container]}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar />

      <View style={{ flex: 1, paddingTop: 105 }}>
        {/* Prayer Feed Header */}
        {/* <PrayerFeedHeader /> */}

        {/* Collapsible Filters */}
        {/* <CollapsibleFilters
          activeFilter={activeFilter}
          activeCategory={activeCategory}
          onFilterChange={setActiveFilter}
          onCategoryChange={setActiveCategory}
          categories={categories}
        /> */}

        {/* Prayer Cards with FlashList */}
        {loading ? (
          <View style={{ alignItems: 'center' }}>
            <VideoLoadingScreen
              loading={loading}
              showText={true}
              loadingText="Loading prayers..."
            />
          </View>
        ) : filteredPrayers.length === 0 ? (
          <View style={{
            alignItems: 'center',
            paddingHorizontal: 32,
          }}>
            <Ionicons name="heart-outline" size={64} color="rgba(255,255,255,0.3)" />
            <Text style={[textStyles.h2, {
              fontSize: 30,
              fontWeight: '300',
              color: 'white',
              marginTop: spacing.lg,
              marginBottom: spacing.md,
              letterSpacing: -0.5,
              fontFamily: 'Roboto-Light',
              textAlign: 'center',
            }]}>No prayers yet</Text>
            <Text style={[textStyles.body, {
              fontSize: 16,
              color: withOpacity(surfaces.text.primary, 0.7),
              textAlign: 'center',
              lineHeight: 24,
              fontFamily: 'Roboto-Regular',
              marginBottom: spacing.xl,
            }]}>
              Be the first to share a prayer with the community
            </Text>
            <TouchableOpacity
              style={{
                backgroundColor: surfaces.glass.medium,
                borderWidth: 1,
                borderColor: 'rgba(255,255,255,0.3)',
                borderRadius: 6,
                paddingHorizontal: 24,
                paddingVertical: 12,
              }}
              onPress={() => router.push('/(tabs)/create')}
            >
              <Text style={{
                fontSize: 16,
                fontWeight: '500',
                color: 'white',
              }}>Create Prayer</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={{ flex: 1, position: 'relative' }}>
            <FlashList
              data={filteredPrayers}
              renderItem={renderPrayerCard}
              style={{ flex: 1, overflow: 'hidden' }}
              contentContainerStyle={{
                paddingHorizontal: spacing.md,
                paddingBottom: 120,
                paddingTop: 20, // Add top padding for fade effect
              }}

              onEndReached={loadMorePrayers}
              onEndReachedThreshold={0.5}
              ListFooterComponent={
                loadingMore ? (
                  <View style={{ padding: 24, alignItems: 'center' }}>
                    <ElegantSpinner size={28} showText text="Loading more..." />
                  </View>
                ) : null
              }
              showsVerticalScrollIndicator={false}
            />

            {/* Fade effect gradient overlay - fades content to transparent */}
            <LinearGradient
              colors={['rgba(26,26,26,1)', 'rgba(26,26,26,0.7)', 'rgba(26,26,26,0)']}
              locations={[0, 0.4, 1]}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 50,
                zIndex: 10,
                pointerEvents: 'none',
              }}
            />
          </View>
        )}
      </View>

      {/* Floating Action Button */}
      <FloatingActionButton
        onPress={() => router.push('/(tabs)/create')}
        icon="add"
        accessibilityLabel="Create new prayer"
      />

      {/* Modals */}
      <PremiumUpgrade
        isOpen={showUpgradeModal}
        onClose={closeUpgradeModal}
        onUpgrade={upgradeToPremium}
        trigger={upgradeReason}
      />
      <ShareModal isOpen={shareModalOpen} onClose={() => setShareModalOpen(false)} />
    </View>
  );
}
