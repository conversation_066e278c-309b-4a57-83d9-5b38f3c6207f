import { Tabs } from 'expo-router';
import { View, Dimensions } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';
import { spacing } from '../../lib/design';

const { width: screenWidth } = Dimensions.get('window');

export default function TabLayout() {
  // const colorScheme = useColorScheme();

  return (
    <Tabs
      screenOptions={{
        tabBarStyle: {
          backgroundColor: '#1a1a1a', // Solid dark background
          height: 72,
          paddingTop: 4,
          paddingBottom: 16,
          paddingHorizontal: 6,
          position: 'absolute',
          // Narrower tab bar with balanced margins - wider than content but not full width
          marginHorizontal: spacing.xl, // 32px margins on both sides
          bottom: spacing.md,
          borderRadius: 16,
          borderWidth: 1,
          borderColor: 'rgba(255, 255, 255, 0.08)',
          overflow: 'hidden',
          // Subtle shadow for floating effect
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.3,
          shadowRadius: 16,
          elevation: 12,
        },
        tabBarActiveTintColor: '#ffffff',
        tabBarInactiveTintColor: 'rgba(255, 255, 255, 0.5)',
        headerShown: false,
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Roboto-Regular', // standard body font
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Prayers',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons
              name={focused ? 'heart' : 'heart-outline'}
              color={color as string}
              size={22}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          title: 'Notifications',
          tabBarIcon: ({ color, focused }) => (
            <Ionicons
              name={focused ? 'notifications' : 'notifications-outline'}
              color={color as string}
              size={22}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          href: null, // Hide from tab bar
        }}
      />
      <Tabs.Screen
        name="impact"
        options={{
          title: 'Impact',
          tabBarIcon: ({ color, size: _size, focused }) => (
            <Ionicons
              name={focused ? 'analytics' : 'analytics-outline'}
              color={color as string}
              size={22}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size: _size, focused }) => (
            <Ionicons
              name={focused ? 'person' : 'person-outline'}
              color={color as string}
              size={22}
            />
          ),
        }}
      />
    </Tabs>
  );
}
