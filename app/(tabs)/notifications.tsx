import { View, Text, ScrollView, StyleSheet, Pressable } from 'react-native';
import Card from '../../components/ui/Card';
import { spacing, radius, surfaces, colors, withOpacity } from '../../lib/design';
import { tokens } from '../../lib/design/tokens';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import GradientBackground from '../../components/GradientBackground';
import AppTopBar from '../../components/AppTopBar';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeInUp } from 'react-native-reanimated';
import { useAuth } from '../../contexts/AuthContext';
import ProfileImage from '../../components/ProfileImage';
// Remove duplicate import

// Mock notification data - in a real app, this would come from your backend
interface Notification {
  id: string;
  type: 'prayer_comment' | 'prayer_join' | 'prayer_answered' | 'prayer_live' | 'prayer_outcome';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  prayerId?: string;
  userId?: string;
  userProfile?: {
    display_name?: string;
    full_name?: string;
    avatar_url?: string;
  };
}

const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'prayer_comment',
    title: 'New Prayer Response',
    message: 'Sarah commented on your prayer "Healing for my mother"',
    timestamp: '2 minutes ago',
    isRead: false,
    prayerId: 'prayer-1',
    userId: 'user-1',
    userProfile: {
      display_name: 'Sarah Johnson',
      avatar_url: undefined,
    },
  },
  {
    id: '2',
    type: 'prayer_join',
    title: 'Someone Joined Your Prayer',
    message: 'Michael is now praying with you for "Job Interview Success"',
    timestamp: '15 minutes ago',
    isRead: false,
    prayerId: 'prayer-2',
    userId: 'user-2',
    userProfile: {
      display_name: 'Michael Chen',
      avatar_url: undefined,
    },
  },
  {
    id: '3',
    type: 'prayer_live',
    title: 'Prayer Going Live',
    message: 'Emma started a live prayer session for "Family Unity"',
    timestamp: '1 hour ago',
    isRead: true,
    prayerId: 'prayer-3',
    userId: 'user-3',
    userProfile: {
      display_name: 'Emma Wilson',
      avatar_url: undefined,
    },
  },
  {
    id: '4',
    type: 'prayer_answered',
    title: 'Prayer Answered! 🙏',
    message: 'David shared an outcome for "Financial Breakthrough"',
    timestamp: '3 hours ago',
    isRead: true,
    prayerId: 'prayer-4',
    userId: 'user-4',
    userProfile: {
      display_name: 'David Rodriguez',
      avatar_url: undefined,
    },
  },
  {
    id: '5',
    type: 'prayer_join',
    title: 'Prayer Circle Growing',
    message: '5 more people joined your prayer "World Peace"',
    timestamp: '1 day ago',
    isRead: true,
    prayerId: 'prayer-5',
  },
];

function NotificationItem({
  notification,
  onPress,
}: {
  notification: Notification;
  onPress: () => void;
}) {
  // Remove old tokens usage

  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'prayer_comment':
        return 'chatbubble';
      case 'prayer_join':
        return 'people';
      case 'prayer_live':
        return 'radio';
      case 'prayer_answered':
        return 'checkmark-circle';
      case 'prayer_outcome':
        return 'heart';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = () => {
    switch (notification.type) {
      case 'prayer_comment':
        return colors.info[500];
      case 'prayer_join':
        return colors.live[400];
      case 'prayer_live':
        return colors.live[400];
      case 'prayer_answered':
        return colors.success[300];
      case 'prayer_outcome':
        return colors.error[400];
      default:
        return surfaces.text.secondary;
    }
  };

  return (
    <Pressable onPress={onPress}>
      <Card
        variant={!notification.isRead ? 'medium' : 'light'}
        style={[styles.notificationCardStyle, !notification.isRead && styles.unreadNotification]}
      >
        <View style={styles.notificationContent}>
          <View style={styles.notificationLeft}>
            {notification.userProfile ? (
              <ProfileImage
                avatarUrl={notification.userProfile.avatar_url}
                displayName={
                  notification.userProfile.display_name ||
                  notification.userProfile.full_name ||
                  'Anonymous'
                }
                fullName={notification.userProfile.full_name}
                size={40}
                style={styles.notificationAvatar}
              />
            ) : (
              <View
                style={[
                  styles.notificationIcon,
                  { backgroundColor: withOpacity(getNotificationColor(), 0.125) },
                ]}
              >
                <Ionicons name={getNotificationIcon()} size={20} color={getNotificationColor()} />
              </View>
            )}
          </View>

          <View style={styles.notificationBody}>
            <Text style={[styles.notificationTitle, !notification.isRead && styles.unreadTitle]}>
              {notification.title}
            </Text>
            <Text style={styles.notificationMessage}>{notification.message}</Text>
            <Text style={styles.notificationTime}>{notification.timestamp}</Text>
          </View>

          {!notification.isRead && <View style={styles.unreadDot} />}
        </View>
      </Card>
    </Pressable>
  );
}

export default function NotificationsScreen() {
  const router = useRouter();
  const { user: _user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const unreadCount = notifications.filter((n) => !n.isRead).length;
  const filteredNotifications =
    filter === 'unread' ? notifications.filter((n) => !n.isRead) : notifications;

  const handleNotificationPress = (notification: Notification) => {
    // Mark as read
    setNotifications((prev) =>
      prev.map((n) => (n.id === notification.id ? { ...n, isRead: true } : n)),
    );

    // Navigate based on notification type
    if (notification.prayerId) {
      router.push(`/prayer-detail?id=${notification.prayerId}`);
    }
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar
        rightComponent={
          unreadCount > 0 ? (
            <Pressable
              onPress={markAllAsRead}
              style={styles.markAllButton}
              accessibilityRole="button"
              accessibilityLabel="Mark all notifications as read"
            >
              <Ionicons name="checkmark-done-outline" size={22} color="rgba(255,255,255,0.9)" />
            </Pressable>
          ) : undefined
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Filter Tabs */}
        <View style={styles.filterContainer}>
          <Pressable
            style={[styles.filterTab, filter === 'all' && styles.activeFilterTab]}
            onPress={() => setFilter('all')}
            accessibilityRole="button"
            accessibilityLabel="Show all notifications"
          >
            <Text style={[styles.filterText, filter === 'all' && styles.activeFilterText]}>
              All ({notifications.length})
            </Text>
          </Pressable>
          <Pressable
            style={[styles.filterTab, filter === 'unread' && styles.activeFilterTab]}
            onPress={() => setFilter('unread')}
            accessibilityRole="button"
            accessibilityLabel="Show unread notifications"
          >
            <Text style={[styles.filterText, filter === 'unread' && styles.activeFilterText]}>
              Unread ({unreadCount})
            </Text>
          </Pressable>
        </View>

        {/* Notifications List */}
        <View style={styles.notificationsList}>
          {filteredNotifications.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Ionicons name="notifications-outline" size={64} color="rgba(255,255,255,0.3)" />
              <Text style={styles.emptyTitle}>
                {filter === 'unread' ? 'No unread notifications' : 'No notifications yet'}
              </Text>
              <Text style={styles.emptySubtitle}>
                {filter === 'unread'
                  ? 'All caught up! Check back later for updates.'
                  : "When people interact with your prayers, you'll see notifications here"}
              </Text>
            </View>
          ) : (
            filteredNotifications.map((notification, index) => (
              <Animated.View
                key={notification.id}
                entering={FadeInUp.delay(index * 50).duration(400)}
              >
                <NotificationItem
                  notification={notification}
                  onPress={() => handleNotificationPress(notification)}
                />
              </Animated.View>
            ))
          )}
        </View>

        {/* Bottom padding */}
        <View style={{ height: 100 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - proper header spacing
  },
  markAllButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    borderRadius: radius.sm,
  },
  markAllText: {
    color: '#007AFF',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    lineHeight: 24,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.md, // 16px sides
    marginBottom: spacing.lg, // 24px section spacing
    gap: spacing.md,
  },
  filterTab: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: surfaces.glass.light,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.15)',
    borderRadius: radius.sm,
    alignItems: 'center',
  },
  activeFilterTab: {
    backgroundColor: 'rgba(0, 122, 255, 0.2)',
    borderColor: 'rgba(0, 122, 255, 0.4)',
  },
  filterText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
  },
  activeFilterText: {
    color: '#007AFF',
  },
  notificationsList: {
    paddingHorizontal: spacing.md, // 16px sides
    gap: 0, // list spacing from card margin
  },
  notificationCardStyle: {
    borderRadius: radius.sm,
    marginBottom: spacing.lg, // 24px standardized card spacing
  },
  unreadNotification: {
    // Handled by Card variant="medium"
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md, // 16px - better content spacing
  },
  notificationLeft: {
    alignItems: 'center',
  },
  notificationAvatar: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationBody: {
    flex: 1,
  },
  notificationTitle: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    marginBottom: spacing.xs, // 4px
  },
  unreadTitle: {
    color: 'white',
  },
  notificationMessage: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: spacing.sm, // 8px - better than 6px
  },
  notificationTime: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 12,
    fontFamily: 'Roboto-Regular',
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
    marginTop: spacing.xs, // 4px
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing['5xl'], // 80px
    paddingHorizontal: spacing.xl, // 32px
  },
  emptyTitle: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    marginTop: spacing.lg, // 24px
    marginBottom: spacing.md, // 16px - consistent with create screen
    letterSpacing: -0.5,
    fontFamily: 'Roboto-Light',
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
  },
});
