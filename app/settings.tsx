import { View, Text, Switch, StyleSheet } from 'react-native';
import G<PERSON><PERSON>Background from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { useRouter } from 'expo-router';

import { surfaces, radius, spacing } from '../lib/design';
import { tokens } from '../lib/design/tokens';
import Card from '../components/ui/Card';

export default function Settings() {
  const router = useRouter();
  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar showBack onBackPress={() => router.back()} />
      <View style={styles.content}>
        <Card variant="light" style={styles.settingCard}>
          <View style={styles.settingItem}>
            <Text style={styles.settingText}>Notifications</Text>
            <Switch />
          </View>
        </Card>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    padding: spacing.md, // 16px
    paddingTop: tokens.spacing['6xl'], // 96px - proper header spacing
  },
  settingCard: {
    borderRadius: radius.sm,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingText: {
    color: surfaces.text.primary,
    fontSize: 16,
  },
});
