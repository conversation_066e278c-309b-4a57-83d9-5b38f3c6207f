import { View, Text, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { useRouter } from 'expo-router';
import Card from '../components/ui/Card';
import { spacing } from '../lib/design';
import { tokens } from '../lib/design/tokens';

export default function Notifications() {
  const router = useRouter();
  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar showBack onBackPress={() => router.back()} />
      <View style={styles.content}>
        <FlatList
          data={[
            { id: '1', title: 'Welcome to the community!', prayerId: null },
            { id: '2', title: '<PERSON> commented on your prayer', prayerId: 'some-prayer-id' },
          ]}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <Card variant="light" style={styles.notificationCard}>
              <TouchableOpacity
                style={styles.notificationItem}
                onPress={() => {
                  if (item.prayerId) {
                    router.push(`/prayer-detail?id=${item.prayerId}`);
                  }
                }}
              >
                <Text style={styles.notificationText}>{item.title}</Text>
              </TouchableOpacity>
            </Card>
          )}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    padding: spacing.md, // 16px
    paddingTop: tokens.spacing['6xl'], // 96px - proper header spacing
  },
  notificationCard: {
    marginBottom: spacing.md, // 16px - better card spacing
  },
  notificationItem: {
    // No background needed - Card provides glass effect
  },
  notificationText: {
    color: 'white',
    fontSize: 16,
  },
});
