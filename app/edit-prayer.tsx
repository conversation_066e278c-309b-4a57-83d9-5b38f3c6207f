import { View, Text, Pressable, ScrollView, TextInput, Switch, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from 'react';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { BlurView } from 'expo-blur';
import { useToast } from '../hooks/useToast';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { supabase } from '../lib/supabase';
import { tokens } from '../lib/design/tokens';
import { textStyles } from '../lib/design';

import { createStyles, variants, mixins, withOpacity, surfaces } from '../lib/design';
import Button from '../components/ui/Button';
import IconButton from '../components/ui/IconButton';
import Badge from '../components/ui/Badge';

interface PrayerData {
  id: string;
  title: string;
  description: string;
  prayerText: string;
  category: string;
  allowsBroadcast: boolean;
  isLive?: boolean;
  isAnswered?: boolean;
  participants: number;
  likes: number;
  comments: number;
}

export default function EditPrayerScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const prayerId = params.prayerId as string;
  const toast = useToast();
  // Remove old tokens usage

  // Generate React Native compatible UUID
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const [formData, setFormData] = useState<PrayerData>({
    id: prayerId || generateUUID(), // Generate proper UUID instead of "1"
    title: '',
    description: '',
    prayerText: '',
    category: 'Health',
    allowsBroadcast: false,
    participants: 0,
    likes: 0,
    comments: 0,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [_hasChanges, setHasChanges] = useState(false);

  // Scheduling state
  const [isScheduled, setIsScheduled] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<Date | null>(null);
  const [durationMinutes, setDurationMinutes] = useState<number | null>(1440);
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [isTimePickerVisible, setTimePickerVisible] = useState(false);

  void setSelectedDate;
  void setSelectedTime;
  void isDatePickerVisible;
  void isTimePickerVisible;

  const scheduledDate = (() => {
    if (!isScheduled || !selectedDate || !selectedTime) return null;
    const d = new Date(selectedDate);
    d.setHours(selectedTime.getHours(), selectedTime.getMinutes(), 0, 0);
    return d.toISOString();
  })();

  const categories = [
    'Health',
    'World Peace',
    'Gratitude',
    'Personal Growth',
    'Family',
    'Abundance',
    'Healing',
    'Protection',
    'Guidance',
  ];

  // Mock data - in real app, fetch from API
  useEffect(() => {
    const mockPrayerData: PrayerData = {
      id: prayerId || generateUUID(), // Generate proper UUID instead of "1"
      title: 'Healing for My Mother',
      description:
        "Please join me in praying for my mother's recovery from surgery. She means everything to our family.",
      prayerText: `Dear Heavenly Father,

We come before You today with hearts full of faith and hope, asking for Your healing touch upon my beloved mother. She is facing surgery, and we place her completely in Your loving hands.

Grant her strength and courage as she prepares for this procedure. Surround her with Your peace that surpasses all understanding, and let her feel Your presence with her every step of the way.

We pray for the medical team - guide their hands with Your wisdom and skill. Give them clarity of mind and steadiness of heart as they care for her.

Lord, we trust in Your perfect timing and Your divine plan. Whether through this surgery or through Your miraculous touch, we believe in Your power to heal and restore.

Comfort our family during this time of uncertainty. Help us to be strong for one another and to find peace in knowing that You are in control.

We thank You for the gift of my mother's life and for all the love she has shared with us. We place our hope and trust in You.

In Jesus' precious name we pray, Amen.`,
      category: 'Health',
      allowsBroadcast: true,
      isLive: false,
      isAnswered: false,
      participants: 12,
      likes: 8,
      comments: 5,
    };
    setFormData(mockPrayerData);
  }, [prayerId]);

  const handleInputChange = (field: keyof PrayerData, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!formData.title.trim()) {
      toast.error({ title: 'Title Required', message: 'Please enter a title for your prayer' });
      return;
    }

    if (!formData.description.trim()) {
      toast.error({
        title: 'Description Required',
        message: 'Please enter a description for your prayer',
      });
      return;
    }

    setIsLoading(true);

    try {
      const updateData: any = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        prayer_text: formData.prayerText.trim() || null,
        category: formData.category,
        allows_broadcast: formData.allowsBroadcast,
      };

      if (isScheduled && scheduledDate) {
        updateData.scheduled_at = scheduledDate;
        updateData.duration_minutes = durationMinutes;
        updateData.is_live = false;
      } else {
        updateData.scheduled_at = null;
        updateData.duration_minutes = null;
        updateData.is_live = true;
      }

      const { error } = await supabase.from('prayers').update(updateData).eq('id', formData.id);

      if (error) throw error;

      toast.success({
        title: 'Prayer Updated',
        message: 'Your prayer has been successfully updated',
      });

      setHasChanges(false);
      router.back();
    } catch (_error) {
      toast.error({
        title: 'Update Failed',
        message: 'Failed to update prayer. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Prayer',
      'Are you sure you want to delete this prayer? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Simulate API call
              await new Promise((resolve) => setTimeout(resolve, 500));

              toast.success({
                title: 'Prayer Deleted',
                message: 'Your prayer has been deleted',
              });

              router.back();
            } catch (_error) {
              toast.error({
                title: 'Delete Failed',
                message: 'Failed to delete prayer. Please try again.',
              });
            }
          },
        },
      ],
    );
  };

  // Broadcast actions removed per new in-place model

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar
        showBack
        onBackPress={() => router.back()}
        rightComponent={
          <IconButton icon="trash-outline" onPress={handleDelete} variant="destructive" size="md" />
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View entering={FadeInUp.duration(600)} style={styles.header}>
          <Text style={styles.title}>Edit Your Prayer</Text>
          <Text style={styles.subtitle}>Update your prayer details and manage broadcasting</Text>
        </Animated.View>

        <View style={styles.form}>
          {/* Status Badges */}
          <Animated.View
            entering={FadeInDown.delay(200).duration(600)}
            style={styles.statusContainer}
          >
            {formData.isLive && <Badge text="LIVE" variant="live" />}
            {formData.isAnswered && <Badge text="ANSWERED" variant="success" />}
          </Animated.View>

          {/* Title Field */}
          <Animated.View entering={FadeInDown.delay(300).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.strong} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <Text style={styles.fieldLabel}>
                  Prayer Title <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  value={formData.title}
                  onChangeText={(text) => handleInputChange('title', text)}
                  placeholder="Enter prayer title..."
                  placeholderTextColor="rgba(255,255,255,0.4)"
                  style={styles.input}
                  maxLength={100}
                />
                <Text style={styles.characterCount}>{formData.title.length}/100</Text>
              </View>
            </BlurView>
          </Animated.View>

          {/* Description Field */}
          <Animated.View entering={FadeInDown.delay(400).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.strong} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <Text style={styles.fieldLabel}>
                  Prayer Request <Text style={styles.required}>*</Text>
                </Text>
                <TextInput
                  value={formData.description}
                  onChangeText={(text) => handleInputChange('description', text)}
                  placeholder="Share what you'd like prayer for..."
                  placeholderTextColor="rgba(255,255,255,0.4)"
                  style={[styles.input, styles.textArea]}
                  maxLength={500}
                  multiline
                  numberOfLines={4}
                />
                <Text style={styles.characterCount}>{formData.description.length}/500</Text>
              </View>
            </BlurView>
          </Animated.View>

          {/* Category Field - Moved to top as requested */}
          <Animated.View entering={FadeInDown.delay(500).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.strong} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <Text style={styles.fieldLabel}>Category</Text>
                <View style={styles.categoryContainer}>
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.categoryScroll}
                  >
                    {categories.map((cat) => (
                      <Pressable
                        key={cat}
                        onPress={() => handleInputChange('category', cat)}
                        style={
                          [
                            styles.categoryChip,
                            formData.category === cat && styles.categoryChipSelected,
                          ] as unknown as any
                        }
                        accessibilityRole="button"
                        accessibilityLabel={`Select category ${cat}`}
                      >
                        <Text
                          style={
                            [
                              styles.categoryChipText,
                              formData.category === cat && styles.categoryChipTextSelected,
                            ] as unknown as any
                          }
                        >
                          {cat}
                        </Text>
                      </Pressable>
                    ))}
                  </ScrollView>
                </View>
              </View>
            </BlurView>
          </Animated.View>

          {/* Prayer Text Field */}
          <Animated.View entering={FadeInDown.delay(600).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.strong} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <Text style={styles.fieldLabel}>Prayer Text</Text>
                <Text style={styles.fieldSubtitle}>
                  Write your prayer or let others pray for your request
                </Text>
                <TextInput
                  value={formData.prayerText}
                  onChangeText={(text) => handleInputChange('prayerText', text)}
                  placeholder="Dear Heavenly Father..."
                  placeholderTextColor="rgba(255,255,255,0.4)"
                  style={[styles.input, styles.textArea]}
                  maxLength={1000}
                  multiline
                  numberOfLines={6}
                />
                <Text style={styles.characterCount}>{formData.prayerText.length}/1000</Text>
              </View>
            </BlurView>
          </Animated.View>

          {/* Enhanced Time & Date Section */}
          <Animated.View entering={FadeInDown.delay(700).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.strong} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <View style={styles.timeHeader}>
                  <Ionicons name="time-outline" size={20} color="rgba(255,255,255,0.8)" />
                  <Text style={styles.fieldLabel}>Prayer Schedule</Text>
                </View>
                <Text style={styles.fieldSubtitle}>
                  When would you like this prayer to be active?
                </Text>

                <View style={styles.timeOptionsContainer}>
                  <Pressable
                    onPress={() => setIsScheduled(false)}
                    style={
                      [
                        styles.timeOption,
                        !isScheduled && styles.timeOptionSelected,
                      ] as unknown as any
                    }
                    accessibilityRole="button"
                    accessibilityLabel="Start now"
                  >
                    <View style={styles.timeOptionContent}>
                      <Ionicons
                        name="flash"
                        size={18}
                        color={withOpacity(tokens.colors.primary[500], 0.9)}
                      />
                      <Text style={styles.timeOptionText}>Start Now</Text>
                      <Text style={styles.timeOptionSubtext}>Begin immediately</Text>
                    </View>
                  </Pressable>

                  <Pressable
                    onPress={() => setIsScheduled(true)}
                    style={
                      [
                        styles.timeOption,
                        isScheduled && styles.timeOptionSelected,
                      ] as unknown as any
                    }
                    accessibilityRole="button"
                    accessibilityLabel="Schedule later"
                  >
                    <View style={styles.timeOptionContent}>
                      <Ionicons name="calendar-outline" size={18} color={surfaces.text.tertiary} />
                      <Text
                        style={
                          [
                            styles.timeOptionText,
                            isScheduled ? undefined : styles.timeOptionTextInactive,
                          ] as unknown as any
                        }
                      >
                        Schedule Later
                      </Text>
                      <Text
                        style={
                          [
                            styles.timeOptionSubtext,
                            isScheduled ? undefined : styles.timeOptionSubtextInactive,
                          ] as unknown as any
                        }
                      >
                        Choose specific time
                      </Text>
                    </View>
                  </Pressable>
                </View>

                <View style={styles.durationContainer}>
                  <Text style={styles.durationLabel}>Prayer Duration</Text>
                  <View style={styles.durationOptions}>
                    {[
                      { label: '24 Hours', value: 1440 },
                      { label: '3 Days', value: 4320 },
                      { label: '1 Week', value: 10080 },
                      { label: 'Ongoing', value: null },
                    ].map((opt) => (
                      <Pressable
                        key={String(opt.value)}
                        onPress={() => setDurationMinutes(opt.value)}
                        style={
                          [
                            styles.durationChip,
                            durationMinutes === opt.value && styles.durationChipSelected,
                          ] as unknown as any
                        }
                        accessibilityRole="button"
                        accessibilityLabel={`Set duration ${opt.label}`}
                      >
                        <Text
                          style={
                            [
                              styles.durationChipText,
                              durationMinutes === opt.value
                                ? undefined
                                : styles.durationChipTextInactive,
                            ] as unknown as any
                          }
                        >
                          {opt.label}
                        </Text>
                      </Pressable>
                    ))}
                  </View>
                </View>

                {isScheduled && (
                  <View style={{ marginTop: tokens.spacing.md }}>
                    <View style={{ flexDirection: 'row', gap: tokens.spacing.md }}>
                      <Pressable
                        onPress={() => setDatePickerVisible(true)}
                        style={[styles.durationChip, styles.durationChipSelected] as unknown as any}
                        accessibilityRole="button"
                        accessibilityLabel="Pick a date"
                      >
                        <Text style={styles.durationChipText}>
                          {selectedDate ? selectedDate.toDateString() : 'Pick a Date'}
                        </Text>
                      </Pressable>
                      <Pressable
                        onPress={() => setTimePickerVisible(true)}
                        style={[styles.durationChip, styles.durationChipSelected] as unknown as any}
                        accessibilityRole="button"
                        accessibilityLabel="Pick a time"
                      >
                        <Text style={styles.durationChipText}>
                          {selectedTime
                            ? selectedTime.toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit',
                              })
                            : 'Pick a Time'}
                        </Text>
                      </Pressable>
                    </View>
                  </View>
                )}
              </View>
            </BlurView>
          </Animated.View>

          {/* Broadcasting Settings */}
          <Animated.View entering={FadeInDown.delay(800).duration(600)} style={styles.fieldCard}>
            <BlurView intensity={tokens.blur.light} tint="dark" style={styles.fieldBlur}>
              <View style={styles.fieldContent}>
                <View style={styles.broadcastHeader}>
                  <Ionicons name="radio" size={20} color="rgba(255,255,255,0.8)" />
                  <Text style={styles.fieldLabel}>Live Broadcasting</Text>
                </View>
                <Text style={styles.fieldSubtitle}>Allow others to join you in live prayer</Text>

                <View style={styles.switchContainer}>
                  <View style={styles.switchContent}>
                    <Text style={styles.switchLabel}>Enable Live Broadcasting</Text>
                    <Text style={styles.switchSubtext}>
                      Others can join and pray with you in real-time
                    </Text>
                  </View>
                  <Switch
                    value={formData.allowsBroadcast}
                    onValueChange={(value) => handleInputChange('allowsBroadcast', value)}
                    trackColor={{ false: 'rgba(255,255,255,0.2)', true: 'rgba(139, 92, 246, 0.3)' }}
                    thumbColor={formData.allowsBroadcast ? '#8b5cf6' : 'rgba(255,255,255,0.8)'}
                  />
                </View>

                {formData.allowsBroadcast && (
                  <View style={styles.broadcastNotice}>
                    <Ionicons name="information-circle" size={16} color="rgba(139, 92, 246, 0.8)" />
                    <Text style={styles.broadcastNoticeText}>
                      Your prayer will be available for live broadcasting after creation
                    </Text>
                  </View>
                )}
              </View>
            </BlurView>
          </Animated.View>

          {/* Full-width Save Button */}
          <Animated.View
            entering={FadeInDown.delay(900).duration(600)}
            style={styles.saveButtonContainer}
          >
            <Button
              title={isLoading ? 'Saving Prayer...' : 'Save Prayer'}
              onPress={handleSave}
              disabled={isLoading}
              loading={isLoading}
              variant="primary"
              size="lg"
              icon={isLoading ? 'hourglass' : 'heart'}
              fullWidth
            />
          </Animated.View>

          {/* Bottom padding */}
          <View style={{ height: 100 }} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = createStyles((tokens) => ({
  container: {
    flex: 1,
    backgroundColor: tokens.colors.background.primary,
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - consistent with other screens
  },
  header: {
    alignItems: 'center' as const,
    paddingHorizontal: tokens.spacing.lg, // 24px - consistent header padding
    marginBottom: tokens.spacing.xl, // 32px
    paddingTop: tokens.spacing.sm, // 8px
  },
  title: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    marginBottom: tokens.spacing.md, // 16px - consistent with create screen
    letterSpacing: -0.5,
    fontFamily: 'Roboto-Light',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
  },
  form: {
    paddingHorizontal: tokens.spacing.md, // 16px - consistent content padding
  },
  statusContainer: {
    flexDirection: 'row' as const,
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
    paddingHorizontal: tokens.spacing.sm,
  },
  liveBadge: {
    ...variants.status.live,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    borderRadius: tokens.radius.xs,
    paddingHorizontal: tokens.spacing.sm,
    paddingVertical: tokens.spacing.xs,
    gap: tokens.spacing.xs,
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: tokens.semanticColors.status.live.dot,
  },
  liveText: {
    ...textStyles.caption,
    fontWeight: '500' as const,
    color: tokens.semanticColors.status.live.text,
  },
  answeredBadge: {
    ...variants.status.answered,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    borderRadius: tokens.radius.xs,
    paddingHorizontal: tokens.spacing.sm,
    paddingVertical: tokens.spacing.xs,
    gap: tokens.spacing.xs,
  },
  answeredText: {
    ...textStyles.caption,
    fontWeight: '500' as const,
    color: tokens.semanticColors.status.answered.text,
  },
  fieldCard: {
    marginBottom: tokens.spacing.lg,
    borderRadius: tokens.radius.xl,
    overflow: 'hidden' as const,
  },
  fieldBlur: {
    borderRadius: tokens.radius.xl,
  },
  fieldContent: {
    ...mixins.glassCard('light'),
    padding: tokens.spacing.lg,
  },
  fieldLabel: {
    ...tokens.typography.heading.h4,
    color: tokens.colors.text.primary,
    marginBottom: tokens.spacing.sm,
  },
  required: {
    color: tokens.colors.error[400],
  },
  input: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    color: tokens.colors.text.primary,
    ...textStyles.body,
    paddingVertical: tokens.spacing.sm,
  },
  characterCount: {
    ...textStyles.caption,
    color: tokens.colors.text.quaternary,
    textAlign: 'right' as const,
    marginTop: tokens.spacing.sm,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  fieldSubtitle: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.tertiary,
    marginBottom: tokens.spacing.md,
  },
  // Category styles
  categoryContainer: {
    marginTop: tokens.spacing.sm,
  },
  categoryScroll: {
    flexGrow: 0,
  },
  categoryChip: {
    backgroundColor: tokens.colors.surface.glassMedium,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    borderRadius: 20,
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    marginRight: tokens.spacing.md,
  },
  categoryChipSelected: {
    backgroundColor: withOpacity(tokens.colors.primary[500], 0.3),
    borderColor: withOpacity(tokens.colors.primary[500], 0.5),
  },
  categoryChipText: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.secondary,
  },
  categoryChipTextSelected: {
    color: tokens.colors.text.primary,
  },
  // Time & Date styles
  timeHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.xs,
  },
  timeOptionsContainer: {
    flexDirection: 'row' as const,
    gap: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
  },
  timeOption: {
    flex: 1,
    backgroundColor: tokens.colors.surface.glassLight,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassMedium,
    borderRadius: tokens.radius.xl,
    padding: tokens.spacing.md,
  },
  timeOptionSelected: {
    backgroundColor: withOpacity(tokens.colors.primary[500], 0.2),
    borderColor: withOpacity(tokens.colors.primary[500], 0.4),
  },
  timeOptionContent: {
    alignItems: 'center' as const,
    gap: tokens.spacing.xs,
  },
  timeOptionText: {
    ...tokens.typography.body.small,
    fontWeight: '500' as const,
    color: tokens.colors.text.primary,
  },
  timeOptionTextInactive: {
    color: tokens.colors.text.tertiary,
  },
  timeOptionSubtext: {
    ...textStyles.caption,
    color: withOpacity(tokens.colors.primary[500], 0.8),
  },
  timeOptionSubtextInactive: {
    color: tokens.colors.text.quaternary,
  },
  durationContainer: {
    marginTop: tokens.spacing.sm,
  },
  durationLabel: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.secondary,
    marginBottom: tokens.spacing.md,
  },
  durationOptions: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: tokens.spacing.sm,
  },
  durationChip: {
    backgroundColor: tokens.colors.surface.glassMedium,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    borderRadius: tokens.spacing.md,
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.xs,
  },
  durationChipSelected: {
    backgroundColor: withOpacity(tokens.colors.primary[500], 0.3),
    borderColor: withOpacity(tokens.colors.primary[500], 0.5),
  },
  durationChipText: {
    ...textStyles.caption,
    color: tokens.colors.text.primary,
  },
  durationChipTextInactive: {
    color: tokens.colors.text.tertiary,
  },
  // Broadcasting styles
  broadcastHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.xs,
  },
  switchContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    marginTop: tokens.spacing.sm,
  },
  switchContent: {
    flex: 1,
  },
  switchLabel: {
    ...textStyles.body,
    color: tokens.colors.text.primary,
  },
  switchSubtext: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.tertiary,
    marginTop: tokens.spacing.xs, // 4px - better than 2px
  },
  broadcastNotice: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: tokens.spacing.sm,
    marginTop: tokens.spacing.md,
    padding: tokens.spacing.md,
    backgroundColor: withOpacity(tokens.colors.primary[500], 0.1),
    borderRadius: tokens.radius.sm,
    borderWidth: 1,
    borderColor: withOpacity(tokens.colors.primary[500], 0.2),
  },
  broadcastNoticeText: {
    flex: 1,
    ...textStyles.caption,
    color: withOpacity(tokens.colors.primary[500], 0.9),
  },
  // Save button styles
  saveButtonContainer: {
    marginTop: tokens.spacing.sm,
    marginBottom: tokens.spacing.xl,
  },
}));
