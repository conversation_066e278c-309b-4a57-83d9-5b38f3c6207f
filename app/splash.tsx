import { View, Text, StyleSheet } from 'react-native';
import Button from '../components/ui/Button';
import { Video, ResizeMode } from 'expo-av';
import { useRouter } from 'expo-router';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { useAuth } from '../contexts/AuthContext';
import { radius } from '../lib/design';

// const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const router = useRouter();
  const { user: _user, loading: _loading } = useAuth();
  // Remove old tokens usage

  // AuthGuard will handle redirecting logged-in users

  return (
    <View style={styles.container}>
      {/* Background Video */}
      <Video
        source={require('../assets/videos/ec6b30f1-ee91-4c1a-8893-b258f7bf127b_3_720_N.mp4')}
        style={styles.backgroundImage}
        shouldPlay
        isLooping
        isMuted
        resizeMode={ResizeMode.COVER}
      />

      {/* Logo in top left */}
      <View style={styles.logoContainer}>
        <View style={styles.logo}>
          <Text style={styles.logoText}>P</Text>
        </View>
      </View>

      {/* Main title with slide-up animation from bottom - Stacked like reference image */}
      <Animated.View entering={FadeInUp.delay(300).duration(1000)} style={styles.titleContainer}>
        <Text style={styles.mainTitle}>
          The{'\n'}Power of{'\n'}Many
        </Text>
      </Animated.View>

      {/* Subtitle with slide-up animation from bottom - Prayer community focus */}
      {/* <Animated.View
          entering={FadeInUp.delay(600).duration(800)}
          style={styles.subtitleContainer}
        >
          <Text style={styles.subtitle}>Where hearts unite{'\n'}in prayer.</Text>
        </Animated.View> */}

      {/* Description with slide-up animation from bottom - Prayer community support */}
      {/* <Animated.View
          entering={FadeInUp.delay(800).duration(800)}
          style={styles.descriptionContainer}
        >
          <Text style={styles.description}>
            Join a caring community where members uplift each other through prayer. Share your requests, offer support to others, and experience the strength that comes from praying together.
          </Text>
        </Animated.View> */}

      {/* Join Button with Backdrop Blur */}
      <Animated.View
        entering={FadeInDown.delay(1000).duration(600)}
        style={styles.joinButtonContainer}
      >
        <Button
          title="Join the community"
          onPress={() => router.push('/onboarding')}
          variant="primary"
          fullWidth
        />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    zIndex: 0,
  },

  // Top gradient overlay - same height as original
  topGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 181,
  },
  // Logo container - positioned in top left
  logoContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  logo: {
    width: 36,
    height: 36,
    backgroundColor: 'white',
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoText: {
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
  },
  // Title positioned exactly like in the reference image - left side, stacked
  titleContainer: {
    // position: 'absolute',
    top: 200, // Moved up slightly
    left: 24, // Standard left margin
    right: 24, // Standard right margin to prevent cutoff
    height: 300, // Taller for three lines
    justifyContent: 'flex-start',
  },
  mainTitle: {
    fontSize: 85, // Reduced to prevent cutoff but still large
    fontWeight: '400',
    color: 'white',
    letterSpacing: -3,
    lineHeight: 82, // Tight line spacing
    fontFamily: 'Antonio-Regular',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 4 },
    textShadowRadius: 8,
    textAlign: 'left',
  },
  // Subtitle positioned lower like in the reference image
  subtitleContainer: {
    // position: 'absolute',
    top: 240, // Adjusted position
    left: 24,
    right: 24,
    height: 70,
  },
  subtitle: {
    fontSize: 30, // Adjusted size
    fontWeight: '400',
    color: 'white',
    letterSpacing: -0.5,
    fontFamily: 'Archivo-Regular',
    lineHeight: 35,
    textAlign: 'left',
  },
  // Description positioned in lower section like in the reference image
  descriptionContainer: {
    // position: 'absolute',
    top: 250, // Adjusted position
    left: 24,
    right: 24,
    height: 100,
    width: '90%',
  },
  description: {
    fontSize: 16, // Standard size for readability
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: 'Roboto-Regular',
    lineHeight: 22,
    textAlign: 'left',
  },
  // Join button positioned at bottom like in the reference image
  joinButtonContainer: {
    position: 'absolute',
    bottom: 50, // Bottom position to match reference
    left: 24,
    right: 24,
    zIndex: 100,
  },
  joinButtonWrapper: {
    width: '100%', // Use full available width
    height: 70,
    borderRadius: 6,
    overflow: 'hidden',
  },
  joinButton: {
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  joinButtonText: {
    fontSize: 18,
    fontWeight: '500',
    color: 'white',
    fontFamily: 'Roboto-Medium',
    textAlign: 'center',
  },
});
