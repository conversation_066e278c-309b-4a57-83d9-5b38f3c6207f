import { View, Text, ScrollView, StyleSheet, Pressable } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from 'react';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, { FadeInDown, FadeInUp } from 'react-native-reanimated';
import { useToast } from '../hooks/useToast';
import ReportModal from '../components/modals/ReportModal';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import ProfileImage from '../components/ProfileImage';
import VideoLoadingScreen from '../components/VideoLoadingScreen';

import { surfaces, withOpacity, mixins, spacing } from '../lib/design';
import { tokens } from '../lib/design/tokens';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';

interface Prayer {
  id: string;
  title: string;
  description: string;
  time: string;
  date: string;
  participants: number;
  likes: number;
  comments: number;
  isAnswered?: boolean;
  isLive?: boolean;
}

interface UserProfile {
  id: string;
  email: string;
  full_name: string | null;
  display_name: string | null;
  bio: string | null;
  avatar_url: string | null;
  location: string | null;
  website: string | null;
  is_premium: boolean;
  prayer_count: number;
  follower_count: number;
  following_count: number;
  days_active: number;
  comments_made: number;
  prayer_time: number;
  created_at: string;
  updated_at: string;
}

// Helper functions
const getUserDisplayName = (profile: UserProfile) => {
  return profile.display_name || profile.full_name || 'Anonymous User';
};

const formatJoinDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
  });
};

function PrayerCard({ prayer, onPress }: { prayer: Prayer; onPress: () => void }) {
  return (
    <Animated.View entering={FadeInDown.delay(200).duration(600)}>
      <Card variant="light" padding={false}>
        <Pressable
          onPress={onPress}
          style={styles.prayerCardContent}
          accessibilityRole="button"
          accessibilityLabel={`Open prayer ${prayer.title}`}
        >
          <View style={styles.prayerHeader}>
            <Text style={styles.prayerTitle}>{prayer.title}</Text>
            {prayer.isAnswered && <Badge text="ANSWERED" variant="success" />}
            {prayer.isLive && <Badge text="LIVE" variant="live" />}
          </View>
          <Text style={styles.prayerDescription} numberOfLines={2}>
            {prayer.description}
          </Text>
          <View style={styles.prayerFooter}>
            <Text style={styles.prayerTime}>
              {prayer.time} • {prayer.date}
            </Text>
            <View style={styles.prayerStats}>
              <View style={styles.statItem}>
                <Ionicons
                  name="heart-outline"
                  size={14}
                  color={withOpacity(surfaces.text.primary, 0.6)}
                />
                <Text style={styles.statText}>{prayer.likes}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons
                  name="chatbubble-outline"
                  size={14}
                  color={withOpacity(surfaces.text.primary, 0.6)}
                />
                <Text style={styles.statText}>{prayer.comments}</Text>
              </View>
              <View style={styles.statItem}>
                <Ionicons
                  name="people-outline"
                  size={14}
                  color={withOpacity(surfaces.text.primary, 0.6)}
                />
                <Text style={styles.statText}>{prayer.participants}</Text>
              </View>
            </View>
          </View>
        </Pressable>
      </Card>
    </Animated.View>
  );
}

export default function UserProfileScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const userId = params.userId as string;
  const fromPrayerId = params.fromPrayerId as string | undefined;
  const toast = useToast();
  const { user } = useAuth();

  const [isFollowing, setIsFollowing] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [userPrayers, setUserPrayers] = useState<Prayer[]>([]);

  useEffect(() => {
    if (userId) {
      fetchUserProfile();
      fetchUserPrayers();
    }
  }, [userId]);

  const fetchUserProfile = async () => {
    try {
      setLoading(true);

      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return;
      }

      if (profile) {
        setUserProfile(profile as any);
        // Check if current user follows this user
        if (user) {
          await checkFollowStatus();
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkFollowStatus = async () => {
    if (!user || !userId) return;

    try {
      const { data, error } = await supabase
        .from('user_follows')
        .select('id')
        .eq('follower_id', user.id)
        .eq('following_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "not found" error
        console.error('Error checking follow status:', error);
        return;
      }

      setIsFollowing(!!data);
    } catch (error) {
      console.error('Error checking follow status:', error);
    }
  };

  const fetchUserPrayers = async () => {
    try {
      const { data: prayers, error } = await supabase
        .from('prayers')
        .select(
          `
          *,
          user_profiles!inner(id, display_name, full_name, avatar_url)
        `,
        )
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching user prayers:', error);
        return;
      }

      if (prayers) {
        // Transform to match Prayer interface
        const transformedPrayers = prayers.map((prayer, _index) => ({
          id: prayer.id,
          title: prayer.title,
          description: prayer.description,
          time: prayer.created_at
            ? new Date(prayer.created_at).toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
              })
            : '',
          date: prayer.created_at
            ? new Date(prayer.created_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
              })
            : '',
          participants: 0, // We don't have participant_count in the prayers table
          likes: prayer.like_count || 0,
          comments: prayer.comment_count || 0,
          isAnswered: prayer.is_answered,
          isLive: prayer.is_live,
        }));

        setUserPrayers(transformedPrayers as any);
      }
    } catch (error) {
      console.error('Error fetching user prayers:', error);
    }
  };

  const handleFollow = async () => {
    if (!userProfile || !user) return;

    const wasFollowing = isFollowing;

    try {
      if (wasFollowing) {
        // Unfollow
        const { error } = await supabase
          .from('user_follows')
          .delete()
          .eq('follower_id', user.id)
          .eq('following_id', userId);

        if (error) {
          console.error('Error unfollowing user:', error);
          toast.showErrorToast('Error', 'Failed to unfollow user');
          return;
        }
      } else {
        // Follow
        const { error } = await supabase.from('user_follows').insert({
          follower_id: user.id,
          following_id: userId,
        });

        if (error) {
          console.error('Error following user:', error);
          toast.showErrorToast('Error', 'Failed to follow user');
          return;
        }
      }

      // Update local state
      setIsFollowing(!wasFollowing);

      // Update follower count optimistically
      setUserProfile((prev) =>
        prev
          ? {
              ...prev,
              follower_count: wasFollowing ? prev.follower_count - 1 : prev.follower_count + 1,
            }
          : null,
      );

      const displayName = getUserDisplayName(userProfile);

      if (wasFollowing) {
        toast.showSuccessToast('Unfollowed', `You unfollowed ${displayName}`);
      } else {
        toast.showSuccessToast('Following!', `You are now following ${displayName}`);
      }
    } catch (error) {
      console.error('Error updating follow status:', error);
      toast.showErrorToast('Error', 'Something went wrong');
    }
  };

  const handleLikeProfile = async () => {
    if (!userProfile || !user) return;

    try {
      // Check if already liked
      const { data: existingLike, error: checkError } = await supabase
        .from('profile_likes')
        .select('id')
        .eq('user_id', user.id)
        .eq('profile_id', userId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking like status:', checkError);
        return;
      }

      if (existingLike) {
        // Unlike
        const { error } = await supabase
          .from('profile_likes')
          .delete()
          .eq('user_id', user.id)
          .eq('profile_id', userId);

        if (error) {
          console.error('Error unliking profile:', error);
          toast.showErrorToast('Error', 'Failed to unlike profile');
          return;
        }

        toast.showSuccessToast(
          'Unliked',
          `You unliked ${getUserDisplayName(userProfile)}'s profile`,
        );
      } else {
        // Like
        const { error } = await supabase.from('profile_likes').insert({
          user_id: user.id,
          profile_id: userId,
        });

        if (error) {
          console.error('Error liking profile:', error);
          toast.showErrorToast('Error', 'Failed to like profile');
          return;
        }

        toast.showSuccessToast('Liked!', `You liked ${getUserDisplayName(userProfile)}'s profile`);
      }
    } catch (error) {
      console.error('Error updating like status:', error);
      toast.showErrorToast('Error', 'Something went wrong');
    }
  };

  const handleMessage = () => {
    if (!userProfile) return;

    toast.info({
      title: 'Messages',
      message: 'Direct messaging feature coming soon!',
    });
  };

  const handleReport = (reason: string, details?: string) => {
    if (!userProfile) return;

    console.log('Reporting user:', userProfile.id, 'Reason:', reason, 'Details:', details);
    // In a real app, send this to your backend
  };

  const handlePrayerPress = (prayer: Prayer) => {
    console.log(
      '🔍 [USER PROFILE] Navigating to prayer detail with ID:',
      prayer.id,
      'Type:',
      typeof prayer.id,
    );
    console.log('🔍 [USER PROFILE] Full prayer object:', JSON.stringify(prayer, null, 2));
    router.push({
      pathname: '/prayer-detail',
      params: {
        id: prayer.id,
        title: prayer.title,
      },
    });
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <GradientBackground showPrayerGradient={true} />
        <AppTopBar showBack onBackPress={() => {
          try {
            // @ts-ignore
            if ((router as any).canGoBack && (router as any).canGoBack()) return router.back();
          } catch {}
          if (fromPrayerId) return router.push({ pathname: '/prayer-detail', params: { id: fromPrayerId } });
          router.push('/(tabs)');
        }} />
        <VideoLoadingScreen loading={loading} showText={true} loadingText="Loading profile..." />
      </View>
    );
  }

  if (!userProfile) {
    return (
      <View style={styles.container}>
        <GradientBackground showPrayerGradient={true} />
        <AppTopBar showBack onBackPress={() => {
          try {
            // @ts-ignore
            if ((router as any).canGoBack && (router as any).canGoBack()) return router.back();
          } catch {}
          if (fromPrayerId) return router.push({ pathname: '/prayer-detail', params: { id: fromPrayerId } });
          router.push('/(tabs)');
        }} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>User not found</Text>
          <Pressable
            onPress={() => router.back()}
            style={styles.errorButton}
            accessibilityRole="button"
            accessibilityLabel="Go back"
          >
            <Text style={styles.errorButtonText}>Go Back</Text>
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar
        showBack
        onBackPress={() => {
          try {
            // @ts-ignore
            if ((router as any).canGoBack && (router as any).canGoBack()) return router.back();
          } catch {}
          if (fromPrayerId) return router.push({ pathname: '/prayer-detail', params: { id: fromPrayerId } });
          router.push('/(tabs)');
        }}
        rightComponent={
          <Pressable
            onPress={() => setShowReportModal(true)}
            style={styles.moreButton}
            accessibilityRole="button"
            accessibilityLabel="More options"
          >
            <Ionicons
              name="ellipsis-horizontal"
              size={24}
              color={withOpacity(surfaces.text.primary, 0.8)}
            />
          </Pressable>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Animated.View entering={FadeInUp.duration(600)} style={styles.profileHeader}>
          {/* Avatar */}
          <View style={styles.avatarContainer}>
            <ProfileImage
              displayName={getUserDisplayName(userProfile)}
              fullName={userProfile.full_name}
              avatarUrl={userProfile.avatar_url}
              size={96}
            />
            {userProfile.is_premium && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark-circle" size={16} color="#60a5fa" />
              </View>
            )}
          </View>

          {/* Name and Bio */}
          <Text style={styles.profileName}>{getUserDisplayName(userProfile)}</Text>
          {userProfile.bio && <Text style={styles.profileBio}>{userProfile.bio}</Text>}
          <Text style={styles.joinedDate}>Joined {formatJoinDate(userProfile.created_at)}</Text>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statContainer}>
              <Text style={styles.statNumber}>{userProfile.follower_count}</Text>
              <Text style={styles.statLabel}>Followers</Text>
            </View>
            <View style={styles.statContainer}>
              <Text style={styles.statNumber}>{userProfile.following_count}</Text>
              <Text style={styles.statLabel}>Following</Text>
            </View>
            <View style={styles.statContainer}>
              <Text style={styles.statNumber}>{userProfile.prayer_count}</Text>
              <Text style={styles.statLabel}>Prayers</Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Pressable
              onPress={handleFollow}
              style={styles.actionButton}
              accessibilityRole="button"
              accessibilityLabel={isFollowing ? 'Unfollow user' : 'Follow user'}
            >
              <Ionicons
                name={isFollowing ? 'person-remove' : 'person-add'}
                size={20}
                color={isFollowing ? '#ef4444' : surfaces.text.primary}
              />
              <Text style={[styles.actionButtonText, isFollowing && styles.followingButtonText]}>
                {isFollowing ? 'Unfollow' : 'Follow'}
              </Text>
            </Pressable>

            <Pressable
              onPress={handleLikeProfile}
              style={styles.actionButton}
              accessibilityRole="button"
              accessibilityLabel="Like profile"
            >
              <Ionicons name="heart-outline" size={20} color={surfaces.text.primary} />
              <Text style={styles.actionButtonText}>Like</Text>
            </Pressable>

            <Pressable
              onPress={handleMessage}
              style={styles.actionButton}
              accessibilityRole="button"
              accessibilityLabel="Message user"
            >
              <Ionicons name="chatbubble-outline" size={20} color={surfaces.text.primary} />
              <Text style={styles.actionButtonText}>Message</Text>
            </Pressable>
          </View>
        </Animated.View>

        {/* Prayers Section */}
        <View style={styles.prayersSection}>
          <Text style={styles.sectionTitle}>Recent Prayers</Text>
          <View style={styles.prayersList}>
            {userPrayers.length > 0 ? (
              userPrayers.map((prayer) => (
                <PrayerCard
                  key={prayer.id}
                  prayer={prayer}
                  onPress={() => handlePrayerPress(prayer)}
                />
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>No prayers yet</Text>
                <Text style={styles.emptySubtext}>This user hasn't shared any prayers</Text>
              </View>
            )}
          </View>
        </View>

        {/* Bottom padding */}
        <View style={{ height: 100 }} />
      </ScrollView>

      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        onSubmitReport={handleReport}
        contentType="user"
        contentAuthor={getUserDisplayName(userProfile)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - proper header spacing
  },
  moreButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: spacing.lg, // 24px
    paddingBottom: spacing.xl, // 32px
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing.lg, // 24px
  },
  avatar: {
    width: 96,
    height: 96,
    ...mixins.glassCard('medium'),
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: 40,
    fontWeight: '300',
    color: 'white',
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    backgroundColor: 'rgba(0,0,0,0.8)',
    borderRadius: 12,
    padding: spacing.xs, // 4px - better than 2px
  },
  profileName: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    marginBottom: spacing.md, // 16px - consistent with create screen
    letterSpacing: -0.5,
    fontFamily: 'Roboto-Light',
    textAlign: 'center',
  },
  profileBio: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
    marginBottom: spacing.lg, // 24px
    maxWidth: 320,
  },
  joinedDate: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.5)',
    marginBottom: spacing.lg, // 24px
  },
  statsContainer: {
    flexDirection: 'row',
    gap: spacing.xl, // 32px
    marginBottom: spacing.xl, // 32px
  },
  statContainer: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '300',
    color: 'white',
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.6)',
    marginTop: spacing.xs, // 4px
  },
  actionButtons: {
    flexDirection: 'row',
    gap: spacing.md, // 16px - better button spacing
    width: '100%',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...mixins.glassCard('subtle'),
    borderRadius: 6,
    paddingVertical: spacing.md, // 16px - better than 12px
    gap: spacing.sm, // 8px
  },
  followButton: {
    backgroundColor: 'rgba(59,130,246,0.2)',
    borderColor: 'rgba(59,130,246,0.4)',
  },
  followingButton: {
    backgroundColor: 'rgba(239,68,68,0.2)',
    borderColor: 'rgba(239,68,68,0.4)',
  },
  actionButtonText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular', // Consistent with body text
    color: '#fff',
  },
  followingButtonText: {
    color: '#ef4444',
  },
  prayersSection: {
    paddingHorizontal: spacing.md, // 16px
  },
  sectionTitle: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    marginBottom: spacing.md, // 16px
  },
  prayersList: {
    gap: 0,
  },
  prayerCardContent: {
    padding: spacing.md, // 16px
    marginBottom: spacing.lg, // 24px - standardized card spacing
  },
  prayerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md, // 16px
  },
  prayerTitle: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'white',
    flex: 1,
  },
  answeredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs, // 4px
    backgroundColor: 'rgba(34,197,94,0.2)',
    borderWidth: 1,
    borderColor: 'rgba(34,197,94,0.3)',
    borderRadius: spacing.sm, // 8px - better than 6px
    paddingHorizontal: spacing.sm, // 8px
    paddingVertical: spacing.xs, // 4px
  },
  answeredText: {
    fontSize: 10,
    fontFamily: 'Roboto-Regular',
    color: '#86efac',
  },
  liveBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm, // 8px - better than 6px
    backgroundColor: 'rgba(239,68,68,0.2)',
    borderWidth: 1,
    borderColor: 'rgba(239,68,68,0.3)',
    borderRadius: spacing.sm, // 8px - better than 6px
    paddingHorizontal: spacing.sm, // 8px
    paddingVertical: spacing.xs, // 4px
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#ef4444',
  },
  liveText: {
    fontSize: 10,
    fontFamily: 'Roboto-Regular',
    color: '#fca5a5',
  },
  prayerDescription: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
    lineHeight: 21,
    marginBottom: spacing.md, // 16px - better text spacing
  },
  prayerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  prayerTime: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.5)',
  },
  prayerStats: {
    flexDirection: 'row',
    gap: spacing.md, // 16px
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs, // 4px
  },
  statText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.6)',
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.lg, // 24px
  },
  errorText: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    textAlign: 'center',
    marginBottom: spacing.md, // 16px
  },
  errorButton: {
    ...mixins.glassCard('subtle'),
    borderRadius: 6,
    paddingHorizontal: spacing.lg, // 24px
    paddingVertical: spacing.md, // 16px - better than 12px
  },
  errorButtonText: {
    fontSize: 16,
    color: '#fff',
    fontFamily: 'Roboto-Regular',
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
    marginTop: spacing.md, // 16px - better than 12px
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: tokens.spacing['2xl'], // 40px
  },
  emptyText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.5)',
    marginBottom: spacing.md, // 16px
  },
  emptySubtext: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.4)',
    textAlign: 'center',
  },
});
