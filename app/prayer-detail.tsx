import { View, Text, TouchableOpacity, ScrollView, TextInput, Modal, Alert } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import Ionicons from '@expo/vector-icons/Ionicons';
import EnhancedCommentsModal from '../components/modals/EnhancedCommentsModal';

import ReportModal from '../components/modals/ReportModal';

import Animated, { FadeInUp } from 'react-native-reanimated';
import { useAuth } from '../contexts/AuthContext';

import {
  supabase,
  PrayerWithProfile,
  PrayerOutcomeInsert,
  PrayerCommentWithProfile,
} from '../lib/supabase';
import { useToast } from '../hooks/useToast';
import ProfileImage from '../components/ProfileImage';
import { PrayerEngagement } from '../components/PrayerEngagement';
import BroadcastingDrawer from '../components/modals/BroadcastingDrawer';
import VideoLoadingScreen from '../components/VideoLoadingScreen';

import { spacing, radius } from '../lib/design/tokens';
import { surfaces, createStyles, textStyles, mixins, withOpacity } from '../lib/design';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import IconButton from '../components/ui/IconButton';
import Badge from '../components/ui/Badge';
import ElegantSpinner from '../components/ui/ElegantSpinner';
import { formatRelativeTime, formatDateTime } from '../utils/dateUtils';
// Using PrayerWithProfile from lib/supabase.ts

interface Comment {
  id: number;
  author: string;
  content: string;
  timestamp: string;
  likes: number;
  isLiked: boolean;
  parentId?: number;
}

function PrayerRequest({ prayer }: { prayer: PrayerWithProfile }) {
  // Using new design system
  return (
    <Animated.View entering={FadeInUp.delay(400)} style={styles.enhancedCardContainer}>
      <Card variant="light" style={styles.enhancedCardStyle}>
        <View style={styles.cardHeader}>
          <Ionicons name="heart-outline" size={20} color="rgba(0,0,0,0.7)" />
          <Text style={styles.enhancedCardTitle}>Prayer Request</Text>
        </View>
        <Text style={styles.enhancedCardText}>{prayer.description}</Text>
      </Card>
    </Animated.View>
  );
}

function PrayerText({ prayer }: { prayer: PrayerWithProfile }) {
  // Temporarily removed tokens
  return (
    <Animated.View entering={FadeInUp.delay(500)} style={styles.enhancedCardContainer}>
      <Card variant="light" style={styles.enhancedCardStyle}>
        <View style={styles.cardHeader}>
          <Ionicons name="book-outline" size={20} color="rgba(0,0,0,0.7)" />
          <Text style={styles.enhancedCardTitle}>Prayer</Text>
        </View>
        <Text style={styles.enhancedCardText}>{prayer.prayer_text || prayer.description}</Text>
        <View style={styles.amenContainer}>
          <Text style={styles.amenText}>🙏 Amen</Text>
        </View>
      </Card>
    </Animated.View>
  );
}

// Removed PrayerActions component - replaced with SimplePrayerActions (no support button)

// Removed unused JoinPrayerButton component - using enhanced broadcast button and PrayerEngagement instead

export default function PrayerDetailScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const toast = useToast();
  // Remove old tokens usage
  const params = useLocalSearchParams();
  const [prayer, setPrayer] = useState<PrayerWithProfile | null>(null);
  const [loading, setLoading] = useState(true);
  // const [isJoined, setIsJoined] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [showPrayingUsers, setShowPrayingUsers] = useState(false);

  const [showReport, setShowReport] = useState(false);
  const [_showBroadcast, _setShowBroadcast] = useState(false);
  const [showBroadcastSheet, setShowBroadcastSheet] = useState(false);
  const [showAddOutcome, setShowAddOutcome] = useState(false);
  const [outcomeText, setOutcomeText] = useState('');
  const [outcomeType, setOutcomeType] = useState<
    'answered' | 'differently_answered' | 'still_waiting' | 'no_longer_needed'
  >('answered');
  const [savingOutcome, setSavingOutcome] = useState(false);
  const [latestComment, setLatestComment] = useState<PrayerCommentWithProfile | null>(null);
  const [showActionMenu, setShowActionMenu] = useState(false);

  // Subscribe to real-time prayer updates (especially is_live status)
  useEffect(() => {
    if (!prayer?.id) return;

    console.log('🔌 Setting up live status subscription for prayer:', prayer.id);

    const subscription = supabase
      .channel(`prayer-live-${prayer.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'prayers',
          filter: `id=eq.${prayer.id}`,
        },
        (payload) => {
          console.log('🔴 Prayer live status changed:', payload.new);
          setPrayer((prev) => (prev ? { ...prev, ...payload.new } : null));
        },
      )
      .subscribe();

    return () => {
      console.log('🔌 Removing live status subscription');
      supabase.removeChannel(subscription);
    };
  }, [prayer?.id]);

  // Fetch latest comment for this prayer
  const fetchLatestComment = async (prayerId: string) => {
    try {
      const { data: comment, error } = await supabase
        .from('prayer_comments')
        .select(
          `
          *,
          user_profiles(*)
        `,
        )
        .eq('prayer_id', prayerId)
        .is('parent_id', null)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        // PGRST116 is "no rows returned"
        console.error('Error fetching latest comment:', error);
        return;
      }

      setLatestComment(comment || null);
    } catch (error) {
      console.error('Error fetching latest comment:', error);
    }
  };

  // Fetch prayer details
  const fetchPrayer = async () => {
    console.log('🔍 fetchPrayer called with params:', JSON.stringify(params, null, 2));
    console.log('🔍 params.id value:', params.id);
    console.log('🔍 params.id type:', typeof params.id);
    console.log('🔍 All params keys:', Object.keys(params));

    if (!params.id) {
      console.error('❌ No prayer ID found in params');
      toast.showErrorToast('Error', 'Prayer ID not found');
      router.back();
      return;
    }

    try {
      const prayerId = Array.isArray(params.id) ? params.id[0] : params.id;
      console.log('🔍 Extracted prayer ID:', prayerId, 'Type:', typeof prayerId);

      // Validate UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(prayerId)) {
        console.error('❌ Invalid UUID format:', prayerId);
        console.error('❌ Full params object:', JSON.stringify(params, null, 2));
        console.error(
          '❌ Current route:',
          router.canGoBack() ? 'Has back history' : 'No back history',
        );
        toast.showErrorToast('Error', `Invalid prayer ID format: ${prayerId}`);
        router.back();
        return;
      }

      // First get the prayer data
      const { data: prayerData, error: prayerError } = await supabase
        .from('prayers')
        .select(
          `
          *,
          user_profiles(*),
          prayer_outcomes(*)
        `,
        )
        .eq('id', prayerId)
        .single();

      if (prayerError) {
        console.error('Error fetching prayer:', prayerError);
        toast.showErrorToast('Error loading prayer', prayerError.message);
        setLoading(false);
        router.back();
        return;
      }

      // Get joined count separately
      const { count: joinedCount } = await supabase
        .from('prayer_joins')
        .select('*', { count: 'exact', head: true })
        .eq('prayer_id', prayerId);

      // Get comments count separately
      const { count: commentsCount } = await supabase
        .from('prayer_comments')
        .select('*', { count: 'exact', head: true })
        .eq('prayer_id', prayerId);

      // Add counts to prayer data
      const prayerWithCounts = {
        ...prayerData,
        joined_count: joinedCount || 0,
        comments_count: commentsCount || 0,
      };

      if (!user) {
        // User not logged in, set prayer without like status
        setPrayer(prayerWithCounts as unknown as PrayerWithProfile);
        setLoading(false);
        return;
      }

      // Set prayer data with counts
      setPrayer(prayerWithCounts as unknown as PrayerWithProfile);

      // Fetch latest comment
      await fetchLatestComment(prayerId);

      setLoading(false);
    } catch (error) {
      console.error('Error fetching prayer:', error);
      toast.showErrorToast('Error loading prayer', 'Something went wrong');
      setLoading(false);
      router.back();
    }
  };

  useEffect(() => {
    fetchPrayer();
  }, [params.id, user]);

  // Real-time subscription for prayer updates
  useEffect(() => {
    if (!prayer?.id) return;

    console.log('🔌 Setting up realtime subscription for prayer:', prayer.id);

    const prayerChannel = supabase
      .channel(`prayer-detail-${prayer.id}`, {
        config: {
          broadcast: { self: false },
        },
      })
      // Removed prayer_joins subscription - handled in PrayerEngagement component
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prayer_comments',
          filter: `prayer_id=eq.${prayer.id}`,
        },
        (payload) => {
          console.log('🔄 Prayer comment change in detail:', payload);
          // Refresh prayer data and latest comment when comments change
          setTimeout(() => {
            fetchPrayer();
            if (prayer?.id) {
              fetchLatestComment(prayer.id);
            }
          }, 100);
        },
      )
      .subscribe((status) => {
        console.log('🔌 Prayer detail subscription status:', status);
      });

    return () => {
      console.log('🔌 Removing prayer detail subscription...');
      supabase.removeChannel(prayerChannel);
    };
  }, [prayer?.id]);

  // Pre-fill form when editing existing outcome
  useEffect(() => {
    if (prayer?.prayer_outcomes && showAddOutcome) {
      setOutcomeText(prayer.prayer_outcomes.outcome_text);
      setOutcomeType((prayer.prayer_outcomes.outcome_type as any) || 'answered');
    } else if (!showAddOutcome) {
      setOutcomeText('');
      setOutcomeType('answered');
    }
  }, [prayer?.prayer_outcomes, showAddOutcome]);

  const [comments, setComments] = useState<Comment[]>([
    {
      id: 1,
      author: 'Eggbert Hallonsås',
      content:
        'Joining you in prayer. May you find strength and peace during this time. Sending love and light your way. 🙏',
      timestamp: '4 hours ago',
      likes: 88,
      isLiked: true,
    },
    {
      id: 2,
      author: 'Sarah Palin',
      content: "Praying for you and your family. God's grace is sufficient. 💙",
      timestamp: '4 min ago',
      likes: 5,
      isLiked: false,
      parentId: 1,
    },
    {
      id: 3,
      author: 'AllComplaints',
      content: "Holding you in prayer. May God's healing touch be upon you and bring you comfort.",
      timestamp: '1 hour ago',
      likes: 128,
      isLiked: true,
    },
  ]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleAddComment = (content: string, parentId?: number) => {
    const newComment: Comment = {
      id: comments.length + 1,
      author: 'You',
      content,
      timestamp: 'Just now',
      likes: 0,
      isLiked: false,
      parentId,
    };
    setComments([...comments, newComment]);
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleLikeComment = (id: number) => {
    setComments(
      comments.map((comment) =>
        comment.id === id
          ? { ...comment, likes: comment.likes + 1, isLiked: !comment.isLiked }
          : comment,
      ),
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <GradientBackground showPrayerGradient={true} />
        <AppTopBar showBack onBackPress={() => router.back()} />
        <VideoLoadingScreen loading={loading} showText={true} loadingText="Loading prayer..." />
      </View>
    );
  }

  if (!prayer) {
    return (
      <View style={styles.container}>
        <GradientBackground showPrayerGradient={true} />
        <AppTopBar showBack onBackPress={() => router.back()} />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Prayer not found</Text>
        </View>
      </View>
    );
  }

  const isUserPrayer = user?.id === prayer.user_id;



  // Handle deleting prayer
  const handleDeletePrayer = async () => {
    if (!prayer || !user || user.id !== prayer.user_id) {
      toast.showErrorToast('Error', 'You can only delete your own prayers');
      return;
    }

    try {
      const { error } = await supabase
        .from('prayers')
        .delete()
        .eq('id', prayer.id)
        .eq('user_id', user.id); // Extra security check

      if (error) throw error;

      toast.showSuccessToast('Prayer Deleted', 'Your prayer has been removed');
      router.back();
    } catch (error) {
      console.error('Error deleting prayer:', error);
      toast.showErrorToast('Delete Failed', 'Unable to delete prayer');
    }
  };

  const confirmDeletePrayer = () => {
    Alert.alert(
      'Delete Prayer',
      'Are you sure you want to delete this prayer? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: handleDeletePrayer,
        },
      ],
    );
  };

  // Handle saving prayer outcome
  const handleSaveOutcome = async () => {
    if (!prayer || !user || !outcomeText.trim()) {
      toast.showErrorToast('Error', 'Please enter an outcome description');
      return;
    }

    setSavingOutcome(true);
    try {
      const outcomeData: PrayerOutcomeInsert = {
        prayer_id: prayer.id,
        outcome_text: outcomeText.trim(),
        outcome_type: outcomeType,
      };

      // First save the outcome
      const { error: outcomeError } = await supabase.from('prayer_outcomes').upsert(outcomeData, {
        onConflict: 'prayer_id',
      });

      if (outcomeError) {
        console.error('Error saving outcome:', outcomeError);
        toast.showErrorToast('Error', 'Failed to save outcome');
        return;
      }

      // Then mark prayer as answered and stop live broadcast
      const { error: prayerError } = await supabase
        .from('prayers')
        .update({
          is_answered: true,
          is_live: false, // Stop any live broadcast when outcome is added
        })
        .eq('id', prayer.id)
        .eq('user_id', user.id); // Security check

      if (prayerError) {
        console.error('Error updating prayer status:', prayerError);
        toast.showErrorToast('Error', 'Failed to update prayer status');
      } else {
        toast.showSuccessToast(
          'Prayer Completed',
          'Your prayer outcome has been shared and the prayer is now closed',
        );
        setShowAddOutcome(false);
        setOutcomeText('');
        // Refresh prayer data
        fetchPrayer();
      }
    } catch (error) {
      console.error('Error saving outcome:', error);
      toast.showErrorToast('Error', 'Something went wrong');
    } finally {
      setSavingOutcome(false);
    }
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />

      <AppTopBar
        showBack
        onBackPress={() => router.back()}
        rightComponent={
          user?.id === prayer.user_id ? (
            <IconButton
              icon="ellipsis-horizontal"
              onPress={() => setShowActionMenu((v) => !v)}
              variant="ghost"
              size="md"
            />
          ) : undefined
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Enhanced Prayer Header */}
        <Animated.View entering={FadeInUp.delay(200)} style={styles.enhancedHeaderContainer}>
          {/* Prayer Title */}
          <Text style={styles.enhancedPrayerTitle}>{prayer.title}</Text>





          {/* Creator Section */}
          <View style={styles.creatorRow}>
            <ProfileImage
              avatarUrl={prayer.user_profiles?.avatar_url}
              displayName={
                prayer.user_profiles?.display_name || prayer.user_profiles?.full_name || 'Anonymous'
              }
              fullName={prayer.user_profiles?.full_name}
              size={48}
              style={styles.enhancedHostAvatar}
            />
            <View style={{ flex: 1 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', gap: 8 }}>
                <TouchableOpacity
                  onPress={() => {
                    if (isUserPrayer) {
                      router.push('/(tabs)/profile');
                    } else {
                      router.push({ pathname: '/user-profile', params: { userId: prayer.user_id, fromPrayerId: prayer.id } });
                    }
                  }}
                  activeOpacity={0.8}
                  style={{ flexShrink: 1, flexGrow: 1, marginRight: 8 }}
                >
                  <Text style={styles.enhancedHostName} numberOfLines={1} ellipsizeMode="tail">
                    {prayer.user_profiles?.display_name ||
                      prayer.user_profiles?.full_name ||
                      'Anonymous'}
                    {isUserPrayer && <Text style={styles.youLabel}> (You)</Text>}
                  </Text>
                </TouchableOpacity>
                {prayer.category && (
                  <Badge text={prayer.category} variant="category" />
                )}
              </View>
              <View style={styles.enhancedTimeRow}>
                <Text style={styles.enhancedTimeText}>{formatRelativeTime(prayer.created_at)}</Text>
                {(prayer as any).scheduled_at && (
                  <>
                    {/* <View style={styles.liveSeparator} /> */}
                    {/* <Text style={styles.enhancedTimeText}>Scheduled {formatDateTime((prayer as any).scheduled_at)}</Text> */}
                  </>
                )}
              </View>
            </View>
          </View>

          {/* Prayer Request - now under creator section */}
          

          {/* Quick Meta: Participants & Comments (swapped) */}
          <View style={styles.metaRow}>
            <View style={styles.metaItem}>
              <Text style={styles.metaValue}>{prayer.joined_count || 0}</Text>
              <Text style={styles.metaLabel}>Participants</Text>
            </View>
            <View style={styles.metaDivider} />
            <TouchableOpacity onPress={() => setShowComments(true)} style={styles.metaItem}>
              <Text style={styles.metaValue}>{(prayer as any).comments_count || 0}</Text>
              <Text style={styles.metaLabel}>Comments</Text>
            </TouchableOpacity>
          </View>


  <Card variant="light" style={styles.enhancedCardStyle}>
          <Animated.View entering={FadeInUp.delay(300)} style={{ marginTop: spacing.md, marginBottom: spacing.lg }}>
            <Text style={styles.enhancedCardText}>{prayer.description}</Text>
          </Animated.View>
          </Card>

        </Animated.View>



        {/* Scheduled status */}
        {!!(prayer as any).scheduled_at && (
          <View style={styles.contentCards}>
            <Animated.View entering={FadeInUp.delay(300)} style={styles.enhancedCardContainer}>
              <Card variant="light" style={styles.enhancedCardStyle}>
                <View style={styles.cardHeader}>
                  <Ionicons name="calendar-outline" size={20} color="rgba(255,255,255,0.8)" />
                  <Text style={styles.enhancedCardTitle}>Scheduled</Text>
                </View>
                <Text style={{ color: 'rgba(255,255,255,0.85)', textAlign: 'center' }}>
                  Starts {formatDateTime((prayer as any).scheduled_at)}
                </Text>
                {!!(prayer as any).duration_minutes && (
                  <Text
                    style={{
                      color: 'rgba(255,255,255,0.6)',
                      textAlign: 'center',
                      marginTop: spacing.xs,
                    }}
                  >
                    Duration: {(prayer as any).duration_minutes} minutes
                  </Text>
                )}
              </Card>
            </Animated.View>
          </View>
        )}

        {/* Prayer Content Cards */}
        <View style={styles.contentCards}>
          <PrayerText prayer={prayer} />



          {/* Latest Comment */}
          {latestComment && (
            <Animated.View entering={FadeInUp.delay(550)} style={styles.enhancedCardContainer}>
              <Card variant="light" style={styles.enhancedCardStyle} padding={false}>
                <TouchableOpacity
                  onPress={() => setShowComments(true)}
                  activeOpacity={0.7}
                  style={styles.enhancedCardPadding}
                >
                  <View style={styles.cardHeader}>
                    <Ionicons name="chatbubble-outline" size={20} color="rgba(0,0,0,0.7)" />
                    <Text style={styles.enhancedCardTitle}>Latest Prayer Response</Text>
                    <TouchableOpacity
                      onPress={() => setShowComments(true)}
                      style={styles.viewAllButton}
                    >
                      <Text style={styles.viewAllText}>View All</Text>
                      <Ionicons name="chevron-forward" size={16} color="rgba(0, 122, 255, 0.8)" />
                    </TouchableOpacity>
                  </View>
                  <View style={styles.latestCommentHeader}>
                    <ProfileImage
                      avatarUrl={latestComment.user_profiles?.avatar_url}
                      displayName={
                        latestComment.user_profiles?.display_name ||
                        latestComment.user_profiles?.full_name ||
                        'Anonymous'
                      }
                      fullName={latestComment.user_profiles?.full_name}
                      size={24}
                      style={styles.latestCommentAvatar}
                    />
                    <Text style={styles.latestCommentAuthor}>
                      {latestComment.user_profiles?.display_name ||
                        latestComment.user_profiles?.full_name ||
                        'Anonymous'}
                    </Text>
                    <Text style={styles.latestCommentTime}>
                      {formatRelativeTime(latestComment.created_at)}
                    </Text>
                  </View>
                  <Text style={styles.latestCommentText}>{latestComment.content}</Text>
                </TouchableOpacity>
              </Card>
            </Animated.View>
          )}
        </View>

        {/* Interactive Stats Cards */}
        <Animated.View entering={FadeInUp.delay(600)} style={styles.statsCardsContainer}>
          {/* Praying Card */}
          <TouchableOpacity
            style={styles.statCard}
            onPress={() => setShowPrayingUsers(true)}
            activeOpacity={0.8}
          >
            <View style={styles.statCardBlur}>
              <View style={styles.statCardContent}>
                <View style={styles.statCardIcon}>
                  <Ionicons name="people" size={24} color="rgba(255,255,255,0.8)" />
                </View>
                <View style={styles.statCardInfo}>
                  <Text style={styles.statCardNumber}>{prayer.joined_count || 0}</Text>
                  <Text style={styles.statCardLabel}>praying</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.5)" />
              </View>
            </View>
          </TouchableOpacity>
        </Animated.View>

        {/* Prayer Outcome Section */}
        {isUserPrayer && !prayer.is_answered && (
          <Animated.View entering={FadeInUp.delay(1000)} style={styles.outcomeSection}>
            <View style={styles.addOutcomeCard}>
              <View style={styles.addOutcomeHeader}>
                <Ionicons name="add-circle-outline" size={24} color="rgba(255,255,255,0.7)" />
                <Text style={styles.addOutcomeTitle}>Share Prayer Outcome</Text>
              </View>
              <Text style={styles.addOutcomeSubtitle}>
                Let others know how God moved in this situation
              </Text>
              <Button
                title="Add Outcome"
                onPress={() => setShowAddOutcome(true)}
                variant="primary"
                icon="heart-outline"
                size="sm"
              />
            </View>
          </Animated.View>
        )}

        {prayer.is_answered && prayer.prayer_outcomes && (
          <Animated.View entering={FadeInUp.delay(1000)} style={styles.outcomeSection}>
            <LinearGradient
              colors={['rgba(34,197,94,0.1)', 'rgba(34,197,94,0.05)']}
              style={styles.outcomeCard}
            >
              <View style={styles.outcomeHeader}>
                <Ionicons name="checkmark-circle" size={24} color="#86efac" />
                <Text style={styles.outcomeTitle}>
                  {prayer.prayer_outcomes.outcome_type === 'answered'
                    ? 'Prayer Answered'
                    : prayer.prayer_outcomes.outcome_type === 'differently_answered'
                      ? 'Answered Differently'
                      : prayer.prayer_outcomes.outcome_type === 'still_waiting'
                        ? 'Still Waiting'
                        : 'No Longer Needed'}
                </Text>
              </View>
              <Text style={styles.outcomeText}>God has moved in this situation</Text>
              <View style={styles.testimonialContainer}>
                <Text style={styles.testimonialText}>"{prayer.prayer_outcomes.outcome_text}"</Text>
                <View style={styles.testimonialFooter}>
                  <View style={styles.testimonialAuthor}>
                    <Ionicons name="person-outline" size={14} color="rgba(134, 239, 172, 0.8)" />
                    <Text style={styles.testimonialAuthorText}>Shared with love</Text>
                  </View>
                  <Text style={styles.testimonialTime}>
                    {formatRelativeTime(prayer.prayer_outcomes.created_at)}
                  </Text>
                </View>
              </View>
              {isUserPrayer && (
                <Button
                  title="Edit Outcome"
                  onPress={() => setShowAddOutcome(true)}
                  variant="secondary"
                  icon="create-outline"
                  size="sm"
                />
              )}
            </LinearGradient>
          </Animated.View>
        )}

        {/* Bottom padding */}
        <View style={{ height: 120 }} />
      </ScrollView>

      {/* Prayer Engagement - Standalone button with blur effect */}
      <View style={styles.engagementWrapper}>
        <PrayerEngagement
          prayer={prayer}
          onListenLive={() =>
            router.push({
              pathname: '/(tabs)',
            })
          }
          onStartBroadcast={() => {
            // Show broadcast controls in place instead of navigating
            setShowBroadcastSheet(true);
          }}
        />
      </View>


      {/* Modals */}
      <BroadcastingDrawer
        isOpen={showBroadcastSheet}
        onClose={() => setShowBroadcastSheet(false)}
        title={prayer.title}
        prayerId={prayer.id}
      />

      <EnhancedCommentsModal
        isOpen={showComments}
        onClose={() => setShowComments(false)}
        prayer={prayer}
      />

      <ReportModal
        isOpen={showReport}
        onClose={() => setShowReport(false)}
        onSubmitReport={(reason: string) => {
          console.log('Report submitted:', reason);
          setShowReport(false);
        }}
        contentType="prayer"
      />

      {/* Action (kebab) menu for edit/delete */}
      <Modal
        visible={showActionMenu}
        transparent
        animationType="fade"
        onRequestClose={() => setShowActionMenu(false)}
      >
        <TouchableOpacity
          style={styles.actionMenuOverlay}
          activeOpacity={1}
          onPress={() => setShowActionMenu(false)}
        >
          <View style={styles.actionMenu}>
            <TouchableOpacity
              style={styles.actionMenuItem}
              onPress={() => {
                setShowActionMenu(false);
                router.push({ pathname: '/edit-prayer', params: { prayerId: prayer.id } });
              }}
            >
              <Text style={styles.actionMenuText}>Edit</Text>
            </TouchableOpacity>
            <View style={styles.actionDivider} />
            <TouchableOpacity
              style={styles.actionMenuItem}
              onPress={() => {
                setShowActionMenu(false);
                confirmDeletePrayer();
              }}
            >
              <Text style={styles.actionMenuText}>Delete</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Praying Users Modal */}
      <Modal
        visible={showPrayingUsers}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowPrayingUsers(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.prayingUsersModal}>
            <View style={styles.prayingUsersHeader}>
              <Text style={styles.prayingUsersTitle}>People Praying</Text>
              <TouchableOpacity
                onPress={() => setShowPrayingUsers(false)}
                style={styles.prayingUsersClose}
              >
                <Ionicons name="close" size={24} color="rgba(255,255,255,0.7)" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.prayingUsersList}>
              <View style={styles.prayingUsersContent}>
                <View style={styles.prayingUsersCount}>
                  <Ionicons name="people" size={20} color="rgba(255,255,255,0.7)" />
                  <Text style={styles.prayingUsersCountText}>
                    {prayer.joined_count || 0} people are praying for this request
                  </Text>
                </View>

                {/* Placeholder for actual users list */}
                <View style={styles.prayingUsersPlaceholder}>
                  <Ionicons name="heart" size={48} color="rgba(255,255,255,0.3)" />
                  <Text style={styles.prayingUsersPlaceholderText}>
                    Prayer community details coming soon
                  </Text>
                  <Text style={styles.prayingUsersPlaceholderSubtext}>
                    We're working on showing you who's praying alongside you
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Add Outcome Modal */}
      <Modal
        visible={showAddOutcome}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddOutcome(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.outcomeModal}>
            <View style={styles.outcomeModalHeader}>
              <Text style={styles.outcomeModalTitle}>
                {prayer?.prayer_outcomes ? 'Edit Prayer Outcome' : 'Share Prayer Outcome'}
              </Text>
              <TouchableOpacity
                onPress={() => setShowAddOutcome(false)}
                style={styles.outcomeModalClose}
              >
                <Ionicons name="close" size={24} color="rgba(255,255,255,0.7)" />
              </TouchableOpacity>
            </View>

            <Text style={styles.outcomeModalSubtitle}>
              Let others know how God moved in this situation
            </Text>

            {/* Outcome Type Selector */}
            <View style={styles.outcomeTypeContainer}>
              <Text style={styles.outcomeTypeLabel}>Outcome Type</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.outcomeTypeScroll}
              >
                {(
                  ['answered', 'differently_answered', 'still_waiting', 'no_longer_needed'] as const
                ).map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.outcomeTypeChip,
                      outcomeType === type && styles.outcomeTypeChipSelected,
                    ]}
                    onPress={() => setOutcomeType(type)}
                  >
                    <Text
                      style={[
                        styles.outcomeTypeChipText,
                        outcomeType === type && styles.outcomeTypeChipTextSelected,
                      ]}
                    >
                      {type === 'answered'
                        ? 'Answered'
                        : type === 'differently_answered'
                          ? 'Answered Differently'
                          : type === 'still_waiting'
                            ? 'Still Waiting'
                            : 'No Longer Needed'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Outcome Text Input */}
            <View style={styles.outcomeTextContainer}>
              <Text style={styles.outcomeTextLabel}>Your Story</Text>
              <View style={styles.outcomeTextInput}>
                <TextInput
                  value={outcomeText}
                  onChangeText={setOutcomeText}
                  placeholder="Share how God worked in this situation... Your testimony can encourage others!"
                  placeholderTextColor="rgba(255,255,255,0.3)"
                  multiline
                  numberOfLines={6}
                  textAlignVertical="top"
                  style={styles.outcomeTextArea}
                />
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.outcomeModalActions}>
              <TouchableOpacity
                style={styles.outcomeModalCancel}
                onPress={() => setShowAddOutcome(false)}
              >
                <Text style={styles.outcomeModalCancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.outcomeModalSave, savingOutcome && styles.outcomeModalSaveDisabled]}
                onPress={handleSaveOutcome}
                disabled={savingOutcome || !outcomeText.trim()}
              >
                {savingOutcome ? (
                  <ElegantSpinner size={18} />
                ) : (
                  <>
                    <Ionicons name="heart" size={16} color="white" />
                    <Text style={styles.outcomeModalSaveText}>Share Outcome</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Broadcast Bottom Sheet */}
      {/* Removed BroadcastBottomSheet - using BroadcastingDrawer instead */}
    </View>
  );
}

const styles = createStyles((tokens) => ({
  container: {
    flex: 1,
    backgroundColor: surfaces.background,
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - consistent with other screens
  },
  // Enhanced Content Styles
  contentCards: {
    paddingHorizontal: tokens.spacing.md,
    gap: tokens.spacing.lg,
    marginBottom: tokens.spacing.lg,
  },
  // Interactive Stats Cards
  statsCardsContainer: {
    paddingHorizontal: tokens.spacing.md,
    gap: tokens.spacing.lg,
    marginBottom: tokens.spacing.lg,
  },
  statCard: {
    borderRadius: tokens.radius.xl,
    overflow: 'hidden' as const,
  },
  statCardBlur: {
    ...mixins.glassCard('light'),
  },
  statCardContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    padding: tokens.spacing.lg,
    gap: tokens.spacing.lg,
  },
  statCardIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: tokens.colors.surface.glassMedium,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  statCardInfo: {
    flex: 1,
    gap: spacing.xs, // 4px - better than 2px
  },
  statCardNumber: {
    fontSize: 24,
    fontFamily: 'Roboto-Regular',
    color: tokens.colors.text.primary,
    letterSpacing: -0.5,
  },
  statCardLabel: {
    ...textStyles.body,
    color: tokens.colors.text.secondary,
    textTransform: 'capitalize' as const,
  },
  enhancedActionsContainer: {
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.lg,
  },
  enhancedBroadcastContainer: {
    paddingHorizontal: tokens.spacing.md,
    paddingBottom: tokens.spacing.xl,
  },
  bottomBroadcastContainer: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.xl, // 32px - close to 34px but aligned
    paddingTop: spacing.lg,
  },
  bottomBroadcastButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  enhancedBroadcastButton: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  joined: {
    ...tokens.typography.heading.h4,
    color: tokens.colors.text.primary,
    textAlign: 'center' as const,
  },
  joinedSmall: {
    ...textStyles.caption,
    color: tokens.colors.text.secondary,
    letterSpacing: -0.5,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
  },
  broadcastGradient: {
    padding: spacing.lg, // 24px - better than 20px
  },
  enhancedBroadcastContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.lg,
  },
  broadcastIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: surfaces.glass.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  broadcastTextContainer: {
    flex: 1,
  },
  enhancedBroadcastTitle: {
    fontSize: 30,
    fontWeight: '300',
    color: 'white',
    letterSpacing: -0.5,
    fontFamily: 'Roboto-Light',
    textAlign: 'center',
    marginBottom: spacing.md, // 16px - consistent with create screen
  },
  enhancedBroadcastSubtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
  },
  // Enhanced Card Styles
  enhancedCardContainer: {
    // No margin - handled by parent gap
  },
  enhancedCardStyle: {
    borderRadius: tokens.radius.xl,
  },
  enhancedCardPadding: {
    padding: tokens.spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: tokens.spacing.sm,
    marginBottom: tokens.spacing.lg,
  },
  enhancedCardTitle: {
    ...tokens.typography.heading.h4,
    color: 'rgba(0, 0, 0, 0.9)', // Dark text for bright white cards
    letterSpacing: 0.2,
  },
  enhancedCardText: {
    ...textStyles.body,
    color: 'rgba(0, 0, 0, 0.7)', // Dark text for bright white cards
    textAlign: 'left' as const,
    lineHeight: tokens.lineHeight.lg,
  },
  headerContainer: {
    alignItems: 'center' as const,
    paddingVertical: tokens.spacing.xl,
    paddingHorizontal: tokens.spacing.md,
  },
  // Enhanced Header Styles
  enhancedHeaderContainer: {
    paddingHorizontal: tokens.spacing.md,
    paddingTop: tokens.spacing.lg,
    paddingBottom: tokens.spacing.xl,
  },
  enhancedPrayerTitle: {
    ...textStyles.h2,
    color: tokens.colors.text.primary,
    textAlign: 'left' as const,
    marginBottom: tokens.spacing.lg,
    paddingHorizontal: tokens.spacing.sm,
  },
  categoryBadgeContainer: {
    alignItems: 'center' as const,
    marginBottom: tokens.spacing.xl,
  },
  categoryBadge: {
    backgroundColor: tokens.colors.surface.glassMedium,
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    borderRadius: tokens.spacing.md,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassLight,
  },
  categoryText: {
    ...textStyles.caption,
    color: tokens.colors.text.secondary,
    fontWeight: '600' as const,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.8,
  },
  enhancedHostContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
    ...mixins.glassCard('light'),
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.lg,
  },
  enhancedHostAvatar: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  enhancedHostDetails: {
    flex: 1,
    alignItems: 'flex-start',
  },
  headerStatsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.md,
    marginHorizontal: -spacing.md, // Extend to full width of parent container
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.sm,
    ...mixins.glassCard('subtle'),
  },
  headerStat: {
    flex: 1,
    alignItems: 'center',
    gap: spacing.xs,
  },
  headerStatNumber: {
    color: 'white',
    fontSize: 18,
    fontFamily: 'Roboto-Regular',
    letterSpacing: -0.2,
  },
  headerStatLabel: {
    color: 'rgba(255,255,255,0.65)',
    fontSize: 12,
    fontFamily: 'Roboto-Regular',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  headerStatSeparator: {
    width: 1,
    height: 20,
    backgroundColor: surfaces.border,
    marginHorizontal: spacing.sm,
  },
  enhancedHostName: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto-Regular', // Consistent with body text standard
    marginBottom: spacing.xs, // 4px
  },
  enhancedTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm, // 8px
  },
  enhancedTimeText: {
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent color
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
  },
  liveSeparator: {
    width: 3,
    height: 3,
    backgroundColor: surfaces.text.tertiary,
    borderRadius: 1.5,
  },
  compactLiveBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.2)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
    gap: spacing.xs,
  },
  compactLiveDot: {
    width: 6,
    height: 6,
    backgroundColor: '#FF3B30',
    borderRadius: 3,
  },
  compactLiveText: {
    color: '#FF3B30',
    fontSize: 10,
    fontWeight: 'bold',
    letterSpacing: 0.5,
  },

  prayerTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    textAlign: 'center',
    letterSpacing: -0.5,
    marginBottom: spacing.md, // 16px - consistent with create screen
    maxWidth: 320,
  },
  hostInfoContainer: {
    alignItems: 'center',
    marginBottom: spacing.lg, // 24px
    position: 'relative',
  },
  hostRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  hostAvatar: {
    // ProfileImage component handles its own styling
  },
  hostDetails: {
    alignItems: 'center',
  },

  hostName: {
    color: 'white',
    fontSize: 16,
    fontFamily: 'Roboto-Regular', // Consistent with body text standard
    marginBottom: spacing.xs,
  },
  youLabel: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 14,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  timeText: {
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent color
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
  },
  dot: {
    color: 'rgba(255,255,255,0.4)',
    fontSize: 12,
  },
  broadcastDot: {
    width: 6,
    height: 6,
    backgroundColor: '#38b2ac',
    borderRadius: 3,
  },
  liveBadgeContainer: {
    position: 'absolute',
    top: -16,
    right: -80,
  },
  categoryContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  },

  liveBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs + 2,
    backgroundColor: 'rgba(56,178,172,0.25)',
    borderWidth: 1,
    borderColor: 'rgba(56,178,172,0.4)',
    borderRadius: 16,
    paddingHorizontal: spacing.md + -4,
    paddingVertical: spacing.sm - 2,
    overflow: 'hidden',
    shadowColor: '#38b2ac',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  liveDot: {
    width: 6,
    height: 6,
    backgroundColor: '#4fd1c7',
    borderRadius: 3,
    shadowColor: '#4fd1c7',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 2,
  },
  liveText: {
    color: '#4fd1c7',
    fontSize: 10,
    fontFamily: 'Roboto-Regular',
    letterSpacing: 1.2,
    textTransform: 'uppercase',
  },
  answeredBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: 'rgba(72,187,120,0.25)',
    borderWidth: 1,
    borderColor: 'rgba(72,187,120,0.4)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
    overflow: 'hidden',
    shadowColor: '#48bb78',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  answeredText: {
    color: '#68d391',
    fontSize: 10,
    fontFamily: 'Roboto-Regular',
    letterSpacing: 1.2,
    textTransform: 'uppercase',
  },
  broadcastNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xl,
  },
  broadcastText: {
    color: '#fca5a5',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    lineHeight: 24,
  },

  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xl + spacing.md,
  },
  stat: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontFamily: 'Roboto-Regular',
    color: 'white',
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(255,255,255,0.6)',
    marginTop: spacing.xs,
  },
  requestContainerWrapper: {
    marginHorizontal: spacing.md,
    marginBottom: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  requestContainer: {
    ...mixins.glassCard('subtle'),
    padding: 24,
  },
  requestHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  requestTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  requestText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    lineHeight: 24,
    textAlign: 'center',
    maxWidth: 280,
    alignSelf: 'center',
  },
  prayerContainerWrapper: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.xl + spacing.md,
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  prayerContainer: {
    ...mixins.glassCard('subtle'),
    padding: 24,
  },
  prayerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xl,
  },
  prayerSectionTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  prayerText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(255,255,255,0.8)',
    lineHeight: 24,
    textAlign: 'center',
    fontStyle: 'italic',
    maxWidth: 280,
    alignSelf: 'center',
    marginBottom: spacing.xl,
  },
  amenContainer: {
    alignItems: 'center',
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  amenText: {
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.xl + spacing.md,
  },
  actionButtonContainer: {
    flex: 1,
    borderRadius: 12,
    marginHorizontal: spacing.sm,
    overflow: 'hidden',
  },
  actionButton: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.sm,
    padding: spacing.lg,
    backgroundColor: surfaces.glass.light,
  },
  likedButton: {
    backgroundColor: 'rgba(248,113,113,0.15)',
    borderWidth: 1,
    borderColor: 'rgba(248,113,113,0.3)',
  },
  actionText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(255,255,255,0.8)',
    lineHeight: 24,
  },
  likedText: {
    color: '#fca5a5',
  },
  outcomeSection: {
    paddingHorizontal: spacing.md,
    marginBottom: spacing.xl + spacing.md,
  },
  outcomeCard: {
    borderWidth: 1,
    borderColor: 'rgba(34,197,94,0.3)',
    borderRadius: 12,
    padding: spacing.lg,
  },
  outcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.md,
    marginBottom: spacing.sm,
  },
  outcomeTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  outcomeText: {
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    color: '#86efac',
    textAlign: 'center',
    marginBottom: spacing.xl,
  },
  testimonialContainer: {
    position: 'relative',
  },
  testimonialText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'white',
    lineHeight: 24,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  testimonialFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  testimonialAuthor: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  testimonialAuthorText: {
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(134, 239, 172, 0.8)',
  },
  testimonialTime: {
    fontSize: 12,
    fontFamily: 'Roboto-Regular',
    color: 'rgba(255,255,255,0.6)',
  },
  engagementWrapper: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: tokens.spacing.lg,
    paddingBottom: tokens.spacing.md,
  },
  joinButtonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  joinButton: {
    ...mixins.glassCard('light'),
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
    borderRadius: radius.sm,
  },
  joinedButtonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  joinButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.md,
  },
  joinIcon: {
    width: 24,
    height: 24,
    backgroundColor: surfaces.glass.medium,
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  joinedIcon: {
    backgroundColor: 'rgba(34,197,94,0.3)',
  },
  joinButtonText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'white',
  },
  // Listen button styles
  joinButtonsContainer: {
    gap: spacing.md,
  },
  listenButtonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  listenButton: {
    ...mixins.glassCard('light'),
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
    borderRadius: radius.sm,
  },
  listeningButtonContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  listenButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.md,
  },
  listenIcon: {
    width: 24,
    height: 24,
    backgroundColor: 'rgba(59,130,246,0.2)',
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  listeningIcon: {
    backgroundColor: 'rgba(239,68,68,0.3)',
  },
  listenButtonText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular', // Consistent with body text
    color: 'white',
  },
  listenerCount: {
    backgroundColor: 'rgba(59,130,246,0.3)',
    borderRadius: radius.sm,
    paddingHorizontal: spacing.sm,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  listenerCountText: {
    fontSize: 12,
    fontFamily: 'Roboto-Medium',
    color: 'white',
  },
  finishedContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  finishedCard: {
    borderRadius: radius.sm,
  },
  finishedMessage: {
    ...mixins.glassCard('light'),
    borderRadius: radius.sm,
    padding: spacing.lg,
  },
  finishedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.md,
    marginBottom: 8,
  },
  finishedTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: '#86efac',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  finishedText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingVertical: tokens.spacing.xl + tokens.spacing.lg,
  },
  loadingText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.6)',
    marginTop: 12,
  },
  // Add Outcome Styles
  addOutcomeCard: {
    ...mixins.glassCard('subtle'),
    padding: 20,
  },
  addOutcomeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    gap: spacing.md,
  },
  addOutcomeTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  addOutcomeSubtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
    marginBottom: spacing.xl,
  },
  addOutcomeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(34,197,94,0.2)',
    borderWidth: 1,
    borderColor: 'rgba(34,197,94,0.4)',
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg + 4,
    gap: spacing.sm,
  },
  addOutcomeButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
  },
  editOutcomeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...mixins.glassCard('light'),
    borderRadius: radius.sm,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md - 4,
    gap: spacing.xs + 2,
    alignSelf: 'flex-start',
    marginTop: spacing.xl,
  },
  editOutcomeButtonText: {
    fontSize: 14,
    color: 'rgba(134, 239, 172, 0.8)',
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'flex-end',
  },
  // Praying Users Modal
  prayingUsersModal: {
    backgroundColor: 'rgba(0,0,0,0.95)',
    borderTopLeftRadius: radius.sm,
    borderTopRightRadius: radius.sm,
    maxHeight: '70%',
    minHeight: '50%',
  },
  prayingUsersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  prayingUsersTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
  },
  prayingUsersClose: {
    padding: 4,
  },
  prayingUsersList: {
    flex: 1,
  },
  prayingUsersContent: {
    padding: spacing.lg,
  },
  prayingUsersCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.xl + spacing.md,
    padding: spacing.lg,
    ...mixins.glassCard('light'),
  },
  prayingUsersCountText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
  },
  prayingUsersPlaceholder: {
    alignItems: 'center',
    paddingVertical: spacing.xl + spacing.md,
    gap: spacing.lg,
  },
  prayingUsersPlaceholderText: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  prayingUsersPlaceholderSubtext: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: spacing.xl,
  },
  outcomeModal: {
    backgroundColor: 'rgba(0,0,0,0.9)',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    paddingBottom: 40,
    maxHeight: '80%',
  },
  outcomeModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  outcomeModalTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    color: 'white',
    letterSpacing: -0.5,
    textAlign: 'center',
    flex: 1,
  },
  outcomeModalClose: {
    padding: 4,
  },
  outcomeModalSubtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7), // Consistent with create screen
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: 'Roboto-Regular',
    marginBottom: spacing.xl,
  },
  outcomeTypeContainer: {
    marginBottom: 24,
  },
  outcomeTypeLabel: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'white',
    marginBottom: spacing.md,
  },
  outcomeTypeScroll: {
    flexDirection: 'row',
  },
  outcomeTypeChip: {
    ...mixins.glassCard('light'),
    borderRadius: radius.sm,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    marginRight: spacing.md,
  },
  outcomeTypeChipSelected: {
    backgroundColor: 'rgba(34,197,94,0.2)',
    borderColor: 'rgba(34,197,94,0.4)',
  },
  outcomeTypeChipText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.7)',
  },
  outcomeTypeChipTextSelected: {
    color: 'white',
    fontFamily: 'Roboto-Regular',
  },
  outcomeTextContainer: {
    marginBottom: spacing.xl,
  },
  outcomeTextLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
    marginBottom: spacing.md,
  },
  outcomeTextInput: {
    ...mixins.glassCard('subtle'),
    borderRadius: radius.sm,
    padding: spacing.lg,
    minHeight: 120,
  },
  outcomeTextArea: {
    color: 'white',
    fontSize: 16,
    textAlignVertical: 'top',
    flex: 1,
  },
  outcomeModalActions: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  outcomeModalCancel: {
    flex: 1,
    ...mixins.glassCard('light'),
    borderRadius: radius.sm,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  actionMenuOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'flex-start' as const,
    alignItems: 'flex-end' as const,
    paddingTop: 90,
    paddingRight: tokens.spacing.md,
  },
  actionMenu: {
    ...mixins.glassCard('strong'),
    backgroundColor: surfaces.background,
    borderRadius: tokens.radius.sm,
    paddingVertical: tokens.spacing.xs,
  },
  actionMenuItem: {
    minWidth: 120,
    height: 40,
    justifyContent: 'center' as const,
    paddingHorizontal: tokens.spacing.md,
  },
  actionDivider: {
    height: 1,
    backgroundColor: tokens.colors.surface.glassMedium,
    marginVertical: tokens.spacing.xs,
  },
  actionMenuText: {
    ...textStyles.body,
    color: tokens.colors.text.primary,
  },
  outcomeModalCancelText: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.7)',
  },
  outcomeModalSave: {
    flex: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(34,197,94,0.2)',
    borderWidth: 1,
    borderColor: 'rgba(34,197,94,0.4)',
    borderRadius: radius.sm,
    paddingVertical: spacing.md,
    gap: spacing.md,
  },
  outcomeModalSaveDisabled: {
    opacity: 0.5,
  },
  outcomeModalSaveText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: 'white',
  },
  // Full-width broadcast button styles
  broadcastButtonContainer: {
    marginHorizontal: spacing.md,
    marginTop: spacing.lg,
  },
  fullWidthBroadcastButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  broadcastButtonBlur: {
    borderRadius: 12,
  },
  broadcastButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(139, 92, 246, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(139, 92, 246, 0.3)',
    borderRadius: radius.sm,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.lg,
  },
  broadcastButtonText: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular', // Consistent with body text
    color: 'white',
    flex: 1,
    textAlign: 'center',
    marginLeft: -spacing.xl, // Offset the icon width for perfect centering
  },
  latestCommentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  latestCommentAvatar: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  latestCommentAuthor: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    flex: 1,
  },
  latestCommentTime: {
    color: 'rgba(255,255,255,0.5)',
    fontSize: 12,
  },
  latestCommentText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    lineHeight: 20,
  },
  // New meta row under creator section
  metaRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingHorizontal: tokens.spacing.md,
    marginTop: tokens.spacing.md,
    marginBottom: tokens.spacing.lg,
    gap: tokens.spacing.lg,
  },
  metaItem: {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    minWidth: 120,
  },
  metaValue: {
    ...tokens.typography.heading.h3,
    color: tokens.colors.text.primary,
  },
  metaLabel: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
    marginTop: tokens.spacing.xs,
  },
  metaDivider: {
    width: 1,
    height: 32,
    backgroundColor: tokens.colors.surface.glassBorder,
  },
  creatorRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: tokens.spacing.md,
    paddingHorizontal: tokens.spacing.md,
    marginBottom: tokens.spacing.md,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.sm,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  viewAllText: {
    color: 'rgba(0, 122, 255, 0.8)',
    fontSize: 12,
    fontFamily: 'Roboto-Regular',
  },
}));
