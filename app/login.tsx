import {
  View,
  Text,
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native';
import { componentStyles, textStyles, surfaces, spacing } from '../lib/design';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/useToast';

export default function LoginScreen() {
  const router = useRouter();
  const { signIn, user, loading: authLoading } = useAuth();
  const toast = useToast();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !authLoading) {
      router.replace('/(tabs)');
    }
  }, [user, authLoading]);

  const validateForm = () => {
    if (!email.trim()) {
      toast.showErrorToast('Email is required', 'Please enter your email address');
      return false;
    }

    if (!email.includes('@')) {
      toast.showErrorToast('Invalid email', 'Please enter a valid email address');
      return false;
    }

    if (!password.trim()) {
      toast.showErrorToast('Password is required', 'Please enter your password');
      return false;
    }

    return true;
  };

  const handleSignIn = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const { error } = await signIn(email.trim(), password);

      if (error) {
        console.error('Sign in error:', error);
        if (error.message?.includes('Invalid login credentials')) {
          toast.showErrorToast('Invalid credentials', 'Please check your email and password');
        } else if (error.message?.includes('Email not confirmed')) {
          toast.showErrorToast(
            'Email not verified',
            'Please check your email and click the verification link',
          );
        } else {
          toast.showErrorToast('Sign in failed', error.message || 'Please try again');
        }
      } else {
        toast.showSuccessToast('Welcome back!', 'You have successfully signed in');
        // Navigation will be handled by the useEffect above
      }
    } catch (error) {
      console.error('Sign in error:', error);
      toast.showErrorToast('Sign in failed', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />

      <AppTopBar showBack onBackPress={() => router.back()} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.content}
      >
        {/* Sign In Title */}
        <Text style={styles.title}>Sign in</Text>

        {/* Form */}
        <View style={styles.form}>
          {/* Email Field */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>E-mail</Text>
            <TextInput
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              placeholderTextColor={surfaces.text.tertiary}
              style={styles.input}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            <View style={styles.inputLine} />
          </View>

          {/* Password Field */}
          <View style={styles.inputContainer}>
            <View style={styles.passwordHeader}>
              <Text style={styles.label}>Password</Text>
              <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
                <Text style={styles.showButton}>{showPassword ? 'Hide' : 'Show'}</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              placeholderTextColor={surfaces.text.tertiary}
              style={styles.input}
              secureTextEntry={!showPassword}
            />
            <View style={styles.inputLine} />
          </View>
        </View>

        {/* Join Button */}
        <TouchableOpacity
          style={[styles.joinButton, loading && styles.joinButtonDisabled]}
          onPress={handleSignIn}
          disabled={loading}
        >
          <Text style={styles.joinButtonText}>
            {loading ? 'Signing in...' : 'Join the community'}
          </Text>
        </TouchableOpacity>

        {/* Sign Up Link */}
        <TouchableOpacity style={styles.signUpContainer} onPress={() => router.push('/signup')}>
          <Text style={styles.signUpText}>
            Don't have an account yet? <Text style={styles.signUpLink}>Sign up</Text>
          </Text>
        </TouchableOpacity>

        {/* Forgot Password */}
        <TouchableOpacity style={styles.forgotPassword}>
          <Text style={styles.forgotPasswordText}>Forgot password?</Text>
        </TouchableOpacity>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...componentStyles.container,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  darkOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  topBarTitle: {
    fontSize: 24,
    fontWeight: '300',
    color: 'white',
    letterSpacing: -0.96,
  },
  menuButton: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 120,
    justifyContent: 'center',
  },
  title: {
    ...textStyles.h1,
    textAlign: 'center',
    fontWeight: '300',
    letterSpacing: -1.44,
    marginBottom: spacing.xl,
  },
  form: {
    marginBottom: 60,
  },
  inputContainer: {
    marginBottom: 40,
  },
  label: {
    fontSize: 16,
    color: surfaces.text.secondary,
    marginBottom: 8,
  },
  passwordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  showButton: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
  input: {
    fontSize: 16,
    color: surfaces.text.primary,
    paddingVertical: 8,
  },
  inputLine: {
    height: 1,
    backgroundColor: surfaces.border,
    marginTop: 4,
  },
  joinButton: {
    ...componentStyles.button,
    ...componentStyles.buttonLarge,
    backgroundColor: surfaces.cardHover,
    borderColor: surfaces.borderHover,
    height: 70,
    ...componentStyles.mb_xl,
  },
  joinButtonText: {
    fontSize: 18,
    fontWeight: '500',
    color: surfaces.text.primary,
  },
  joinButtonDisabled: {
    opacity: 0.6,
  },
  signUpContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  signUpText: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
  signUpLink: {
    fontWeight: '600',
  },
  forgotPassword: {
    alignItems: 'center',
  },
  forgotPasswordText: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
});
