import {
  View,
  Text,
  Pressable,
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { componentStyles, surfaces, spacing, radius, withOpacity } from '../lib/design';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/useToast';

export default function SignupScreen() {
  const router = useRouter();
  const { signUp } = useAuth();
  const toast = useToast();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [username, setUsername] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    if (!email.trim()) {
      toast.showErrorToast('Email is required', 'Please enter your email address');
      return false;
    }

    if (!email.includes('@')) {
      toast.showErrorToast('Invalid email', 'Please enter a valid email address');
      return false;
    }

    if (!password.trim()) {
      toast.showErrorToast('Password is required', 'Please enter a password');
      return false;
    }

    if (password.length < 6) {
      toast.showErrorToast('Password too short', 'Password must be at least 6 characters');
      return false;
    }

    if (password !== confirmPassword) {
      toast.showErrorToast("Passwords don't match", 'Please make sure both passwords match');
      return false;
    }

    if (!username.trim()) {
      toast.showErrorToast('Username is required', 'Please choose a username');
      return false;
    }

    return true;
  };

  const handleSignUp = async () => {
    if (!validateForm()) return;

    setLoading(true);

    try {
      const { error } = await signUp(email.trim(), password, {
        full_name: fullName.trim(),
        display_name: username.trim(),
      });

      if (error) {
        console.error('Sign up error:', error);
        toast.showErrorToast('Sign up failed', error.message || 'Please try again');
      } else {
        toast.showSuccessToast(
          'Account created!',
          'Please check your email to verify your account',
        );
        // Navigate to login screen to let user sign in after verification
        router.replace('/login');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      toast.showErrorToast('Sign up failed', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />

      <AppTopBar showBack onBackPress={() => router.back()} />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.content}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          {/* Sign Up Title */}
          <Text style={styles.title}>Sign up</Text>

          {/* Form */}
          <View style={styles.form}>
            {/* Full Name Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Full Name</Text>
              <TextInput
                value={fullName}
                onChangeText={setFullName}
                placeholder="Enter your full name"
                placeholderTextColor={surfaces.text.tertiary}
                style={styles.input}
                autoCapitalize="words"
              />
              <View style={styles.inputLine} />
            </View>

            {/* Username Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Username</Text>
              <TextInput
                value={username}
                onChangeText={setUsername}
                placeholder="Choose a username"
                placeholderTextColor={surfaces.text.tertiary}
                style={styles.input}
                autoCapitalize="none"
              />
              <View style={styles.inputLine} />
            </View>

            {/* Email Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>E-mail</Text>
              <TextInput
                value={email}
                onChangeText={setEmail}
                placeholder="Enter your email"
                placeholderTextColor={surfaces.text.tertiary}
                style={styles.input}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              <View style={styles.inputLine} />
            </View>

            {/* Password Field */}
            <View style={styles.inputContainer}>
              <View style={styles.passwordHeader}>
                <Text style={styles.label}>Password</Text>
                <Pressable
                  onPress={() => setShowPassword(!showPassword)}
                  accessibilityRole="button"
                  accessibilityLabel="Toggle password visibility"
                >
                  <Text style={styles.showButton}>{showPassword ? 'Hide' : 'Show'}</Text>
                </Pressable>
              </View>
              <TextInput
                value={password}
                onChangeText={setPassword}
                placeholder="Create a password"
                placeholderTextColor={surfaces.text.tertiary}
                style={styles.input}
                secureTextEntry={!showPassword}
              />
              <View style={styles.inputLine} />
            </View>

            {/* Confirm Password Field */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Confirm Password</Text>
              <TextInput
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                placeholder="Confirm your password"
                placeholderTextColor="rgba(255,255,255,0.3)"
                style={styles.input}
                secureTextEntry={!showPassword}
              />
              <View style={styles.inputLine} />
            </View>
          </View>

          {/* Create Account Button */}
          <Pressable
            style={[styles.createButton, loading && styles.createButtonDisabled]}
            onPress={handleSignUp}
            disabled={loading}
            accessibilityRole="button"
            accessibilityLabel="Create account"
          >
            <Text style={styles.createButtonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </Pressable>

          {/* Sign In Link */}
          <Pressable
            style={styles.signInContainer}
            onPress={() => router.push('/login')}
            accessibilityRole="button"
            accessibilityLabel="Go to sign in"
          >
            <Text style={styles.signInText}>
              Already have an account? <Text style={styles.signInLink}>Sign in</Text>
            </Text>
          </Pressable>

          {/* Terms */}
          <Text style={styles.terms}>
            By signing up, you agree to our Terms of Service and Privacy Policy
          </Text>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...componentStyles.container,
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  darkOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  topBarTitle: {
    fontSize: 24,
    fontWeight: '300',
    color: 'white',
    letterSpacing: -0.96,
  },
  menuButton: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 120,
  },
  title: {
    fontSize: 36,
    fontWeight: '300',
    color: 'white',
    textAlign: 'center',
    letterSpacing: -1.44,
    marginBottom: 40,
    marginTop: 20,
  },
  form: {
    marginBottom: 40,
  },
  inputContainer: {
    marginBottom: 30,
  },
  label: {
    fontSize: 16,
    color: surfaces.text.secondary,
    marginBottom: spacing.xs,
  },
  passwordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  showButton: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
  input: {
    fontSize: 16,
    color: surfaces.text.primary,
    paddingVertical: spacing.xs,
  },
  inputLine: {
    height: 1,
    backgroundColor: surfaces.border,
    marginTop: spacing.xs,
  },
  createButton: {
    height: 70,
    backgroundColor: withOpacity(surfaces.text.primary, 0.25),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.4),
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  createButtonText: {
    fontSize: 18,
    fontWeight: '500',
    color: surfaces.text.primary,
  },
  createButtonDisabled: {
    opacity: 0.6,
  },
  signInContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  signInText: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
  signInLink: {
    fontWeight: '600',
  },
  terms: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.6),
    textAlign: 'center',
    paddingHorizontal: spacing.md,
    marginBottom: spacing.lg,
  },
});
