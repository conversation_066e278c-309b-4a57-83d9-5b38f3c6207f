import {
  View,
  Text,
  TextInput,
  Pressable,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import GradientBackground from '../components/GradientBackground';
import AppTopBar from '../components/AppTopBar';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/useToast';
import { useProfileImage } from '../hooks/useProfileImage';
import { surfaces, spacing, radius, withOpacity } from '../lib/design';
import Ionicons from '@expo/vector-icons/Ionicons';
import ProfileImage from '../components/ProfileImage';

export default function EditProfile() {
  const router = useRouter();
  const { userProfile, updateProfile } = useAuth();
  const toast = useToast();
  // Remove old tokens usage
  const { uploading: imageUploading, pickAndUploadImage, takePhoto } = useProfileImage();
  const [loading, setLoading] = useState(false);
  const [showImageOptions, setShowImageOptions] = useState(false);
  const [formData, setFormData] = useState({
    fullName: '',
    displayName: '',
    bio: '',
    location: '',
    website: '',
  });

  // Load current profile data
  useEffect(() => {
    if (userProfile) {
      setFormData({
        fullName: userProfile.full_name || '',
        displayName: userProfile.display_name || '',
        bio: userProfile.bio || '',
        location: userProfile.location || '',
        website: userProfile.website || '',
      });
    }
  }, [userProfile]);

  const validateForm = () => {
    if (!formData.displayName.trim()) {
      toast.showErrorToast('Display Name Required', 'Please enter a display name');
      return false;
    }
    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const { error } = await updateProfile({
        full_name: formData.fullName.trim() || null,
        display_name: formData.displayName.trim(),
        bio: formData.bio.trim() || null,
        location: formData.location.trim() || null,
        website: formData.website.trim() || null,
      });

      if (error) {
        console.error('Error updating profile:', error);
        toast.showErrorToast('Update Failed', error.message || 'Failed to update profile');
      } else {
        toast.showSuccessToast('Profile Updated', 'Your profile has been successfully updated');
        router.back();
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.showErrorToast('Update Failed', 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />
      <AppTopBar showBack onBackPress={() => router.back()} />

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <View style={styles.avatarContainer}>
            <ProfileImage
              avatarUrl={userProfile?.avatar_url}
              displayName={formData.displayName}
              fullName={formData.fullName}
              size={80}
              style={styles.avatar}
            />
            <Pressable
              style={styles.changeAvatarButton}
              onPress={() => setShowImageOptions(true)}
              disabled={imageUploading}
              accessibilityRole="button"
              accessibilityLabel="Change profile photo"
            >
              <Ionicons name="camera" size={16} color={surfaces.text.primary} />
              <Text style={styles.changeAvatarText}>
                {imageUploading ? 'Uploading...' : 'Change Photo'}
              </Text>
            </Pressable>
          </View>
        </View>

        {/* Form Fields */}
        <View style={styles.fieldsContainer}>
          {/* Display Name */}
          <View style={styles.fieldContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.fieldLabel}>
                Display Name <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                style={styles.input}
                value={formData.displayName}
                onChangeText={(text) => setFormData({ ...formData, displayName: text })}
                placeholder="How others will see you"
                placeholderTextColor={surfaces.text.tertiary}
              />
            </View>
          </View>

          {/* Full Name */}
          <View style={styles.fieldContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.fieldLabel}>Full Name</Text>
              <TextInput
                style={styles.input}
                value={formData.fullName}
                onChangeText={(text) => setFormData({ ...formData, fullName: text })}
                placeholder="Your full name"
                placeholderTextColor={surfaces.text.tertiary}
              />
            </View>
          </View>

          {/* Bio */}
          <View style={styles.fieldContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.fieldLabel}>Bio</Text>
              <TextInput
                style={[styles.input, styles.textAreaInput]}
                value={formData.bio}
                onChangeText={(text) => setFormData({ ...formData, bio: text })}
                placeholder="Tell others about yourself and your faith journey"
                placeholderTextColor={surfaces.text.tertiary}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>
          </View>

          {/* Location */}
          <View style={styles.fieldContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.fieldLabel}>Location</Text>
              <TextInput
                style={styles.input}
                value={formData.location}
                onChangeText={(text) => setFormData({ ...formData, location: text })}
                placeholder="City, State or Country"
                placeholderTextColor={surfaces.text.tertiary}
              />
            </View>
          </View>

          {/* Website */}
          <View style={styles.fieldContainer}>
            <View style={styles.fieldCard}>
              <Text style={styles.fieldLabel}>Website</Text>
              <TextInput
                style={styles.input}
                value={formData.website}
                onChangeText={(text) => setFormData({ ...formData, website: text })}
                placeholder="https://yourwebsite.com"
                placeholderTextColor="rgba(255,255,255,0.3)"
                keyboardType="url"
                autoCapitalize="none"
              />
            </View>
          </View>
        </View>

        {/* Save Button */}
        <View style={styles.saveButtonContainer}>
          <View style={styles.saveButton}>
            <Pressable
              style={[styles.saveButtonInner, loading && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={loading}
              accessibilityRole="button"
              accessibilityLabel="Save profile changes"
            >
              {loading ? (
                <ActivityIndicator color={surfaces.text.primary} size="small" />
              ) : (
                <>
                  <Ionicons name="checkmark" size={18} color={surfaces.text.primary} />
                  <Text style={styles.saveButtonText}>Save Changes</Text>
                </>
              )}
            </Pressable>
          </View>
        </View>

        {/* Bottom padding */}
        <View style={{ height: 40 }} />
      </ScrollView>

      {/* Image Options Modal */}
      {showImageOptions && (
        <View style={styles.imageOptionsOverlay}>
          <View style={styles.imageOptionsModal}>
            <View style={styles.imageOptionsHeader}>
              <Text style={styles.imageOptionsTitle}>Change Profile Photo</Text>
              <Pressable
                onPress={() => setShowImageOptions(false)}
                style={styles.imageOptionsClose}
                accessibilityRole="button"
                accessibilityLabel="Close image options"
              >
                <Ionicons name="close" size={24} color={withOpacity(surfaces.text.primary, 0.7)} />
              </Pressable>
            </View>

            <View style={styles.imageOptionsButtons}>
              <Pressable
                style={styles.imageOptionButton}
                onPress={async () => {
                  setShowImageOptions(false);
                  await takePhoto();
                }}
                disabled={imageUploading}
                accessibilityRole="button"
                accessibilityLabel="Take a new photo"
              >
                <Ionicons name="camera" size={24} color={surfaces.text.primary} />
                <Text style={styles.imageOptionText}>Take Photo</Text>
              </Pressable>

              <Pressable
                style={styles.imageOptionButton}
                onPress={async () => {
                  setShowImageOptions(false);
                  await pickAndUploadImage();
                }}
                disabled={imageUploading}
                accessibilityRole="button"
                accessibilityLabel="Choose a photo from library"
              >
                <Ionicons name="images" size={24} color={surfaces.text.primary} />
                <Text style={styles.imageOptionText}>Choose from Library</Text>
              </Pressable>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  content: {
    flex: 1,
    paddingTop: 120,
  },
  scrollContainer: {
    paddingHorizontal: 24,
    paddingBottom: 120,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  avatarContainer: {
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    padding: spacing.xl,
    alignItems: 'center',
    width: '100%',
  },
  avatar: {
    marginBottom: spacing.md,
  },
  changeAvatarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
  },
  changeAvatarText: {
    fontSize: 14,
    color: withOpacity(surfaces.text.primary, 0.7),
  },
  fieldsContainer: {
    gap: spacing.lg,
  },
  fieldContainer: {
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  fieldCard: {
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    padding: spacing.lg,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: surfaces.text.primary,
    marginBottom: spacing.sm,
  },
  required: {
    color: '#f87171',
  },
  input: {
    color: surfaces.text.primary,
    fontSize: 16,
    borderWidth: 0,
    paddingVertical: spacing.xs,
    paddingHorizontal: 0,
  },
  textAreaInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  saveButtonContainer: {
    marginTop: spacing.lg,
    borderRadius: radius.sm,
    overflow: 'hidden',
  },
  saveButton: {
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
  },
  saveButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    gap: spacing.xs,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: surfaces.text.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  imageOptionsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageOptionsModal: {
    backgroundColor: 'rgba(0,0,0,0.9)',
    borderRadius: 16,
    padding: spacing.lg,
    width: '80%',
    maxWidth: 300,
  },
  imageOptionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  imageOptionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: surfaces.text.primary,
    flex: 1,
  },
  imageOptionsClose: {
    padding: 4,
  },
  imageOptionsButtons: {
    gap: spacing.md,
  },
  imageOptionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.2),
    borderRadius: radius.sm,
    padding: spacing.md,
    gap: spacing.md,
  },
  imageOptionText: {
    fontSize: 16,
    color: surfaces.text.primary,
    fontWeight: '500',
  },
});
