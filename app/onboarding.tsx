import { View, Text, StyleSheet, ScrollView, Animated } from 'react-native';
import Button from '../components/ui/Button';
import { useRouter } from 'expo-router';
import { useEffect, useRef } from 'react';
import GradientBackground from '../components/GradientBackground';
import Ionicons from '@expo/vector-icons/Ionicons';
import { surfaces, spacing, radius, withOpacity } from '../lib/design';

export default function OnboardingScreen() {
  const router = useRouter();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  // Remove old tokens usage

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <View style={styles.container}>
      <GradientBackground showPrayerGradient={true} />

      {/* Back Button */}
      {/* <BlurView intensity={tokens.blur.strong} style={styles.backButton}>
          <TouchableOpacity 
            style={styles.backButtonInner}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color="white" />
          </TouchableOpacity>
        </BlurView> */}

      {/* Content */}
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            {/* <View style={styles.iconContainer}>
                <Ionicons name="heart" size={32} color="white" />
              </View> */}
            <Text style={styles.title}>Welcome to The Power of Many</Text>
            <Text style={styles.subtitle}>
              Join 45,000+ believers in a community where prayers are shared and amplified together
            </Text>
          </View>

          {/* Premium Trial Card */}
          <View style={styles.premiumCard}>
            {/* <BlurView intensity={tokens.blur.strong} style={styles.premiumIconContainer}>
                <Ionicons name="heart" size={28} color="white" />
              </BlurView> */}
            <Text style={styles.premiumTitle}>Start Your Premium Journey</Text>
            <Text style={styles.premiumSubtitle}>
              Experience unlimited prayers and live broadcasting for 7 days
            </Text>

            {/* Benefits Grid */}
            <View style={styles.benefitsGrid}>
              <View style={styles.benefit}>
                <Ionicons name="checkmark-circle" size={16} color="#86efac" />
                <Text style={styles.benefitText}>Unlimited prayers</Text>
              </View>
              <View style={styles.benefit}>
                <Ionicons name="checkmark-circle" size={16} color="#86efac" />
                <Text style={styles.benefitText}>Live broadcasting</Text>
              </View>
            </View>

            <View style={styles.noCreditCard}>
              <Ionicons name="checkmark-circle" size={16} color="#86efac" />
              <Text style={styles.noCreditCardText}>No credit card required • Cancel anytime</Text>
            </View>

            {/* Start Trial Button */}
            <View style={styles.trialButton}>
              <Button
                title="Start 7-Day Free Trial"
                onPress={() => router.push('/signup')}
                variant="primary"
                icon="heart"
                fullWidth
              />
            </View>

            <Text style={styles.priceText}>Join Premium • Then $9.99/month</Text>
          </View>

          {/* Feature Comparison */}
          <View style={styles.comparisonCard}>
            <Text style={styles.comparisonTitle}>Free vs Premium</Text>

            <View style={styles.comparisonRow}>
              <Text style={styles.featureName}>Prayer posts</Text>
              <View style={styles.comparisonValues}>
                <Text style={styles.freeValue}>Free: 10/month</Text>
                <Text style={styles.premiumValue}>Premium: Unlimited</Text>
              </View>
            </View>

            <View style={styles.comparisonRow}>
              <Text style={styles.featureName}>Live broadcasting</Text>
              <View style={styles.comparisonValues}>
                <Text style={styles.freeValueNo}>Free: ✗</Text>
                <Text style={styles.premiumValueYes}>Premium: ✓</Text>
              </View>
            </View>
          </View>

          {/* Continue Free Button */}
          <View style={styles.freeButton}>
            <Button
              title="Continue with Free Plan"
              onPress={() => router.push('/login')}
              variant="secondary"
              fullWidth
            />
          </View>

          <Text style={styles.upgradeNote}>
            You can upgrade to Premium anytime from your settings
          </Text>
        </Animated.View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 24,
    zIndex: 10,
    width: 44,
    height: 44,
    borderRadius: 6,
    overflow: 'hidden',
  },
  backButtonInner: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: withOpacity(surfaces.text.primary, 0.1),
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.3),
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: 140,
    paddingBottom: 40,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 64,
    height: 64,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    borderRadius: radius.sm,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '300',
    color: surfaces.text.primary,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  subtitle: {
    fontSize: 16,
    color: withOpacity(surfaces.text.primary, 0.7),
    textAlign: 'center',
    lineHeight: 24,
  },
  premiumCard: {
    borderRadius: radius.sm,
    padding: spacing.lg,
    alignItems: 'center',
    marginBottom: spacing.sm,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.4),
  },
  premiumIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.4)',
  },
  premiumTitle: {
    fontSize: 18,
    fontWeight: '300',
    color: surfaces.text.primary,
    marginBottom: 8,
  },
  premiumSubtitle: {
    fontSize: 14,
    color: withOpacity(surfaces.text.primary, 0.7),
    textAlign: 'center',
    marginBottom: 16,
  },
  benefitsGrid: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xs,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  benefitText: {
    fontSize: 12,
    color: surfaces.text.primary,
  },
  noCreditCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  noCreditCardText: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.8),
  },
  trialButton: {
    borderRadius: radius.sm,
    width: '100%',
    marginBottom: spacing.xs,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.5),
  },
  trialButtonInner: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.lg,
    justifyContent: 'center',
  },
  trialButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: surfaces.text.primary,
  },
  priceText: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.6),
  },
  comparisonCard: {
    borderRadius: radius.sm,
    padding: spacing.sm,
    marginBottom: spacing.sm,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.3),
  },
  comparisonTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: surfaces.text.primary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  comparisonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  featureName: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.8),
  },
  comparisonValues: {
    flexDirection: 'row',
    gap: 12,
  },
  freeValue: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.6),
  },
  premiumValue: {
    fontSize: 12,
    color: '#86efac',
  },
  freeValueNo: {
    fontSize: 12,
    color: '#f87171',
  },
  premiumValueYes: {
    fontSize: 12,
    color: '#86efac',
  },
  freeButton: {
    borderRadius: radius.sm,
    marginBottom: spacing.xs,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: withOpacity(surfaces.text.primary, 0.3),
  },
  freeButtonInner: {
    backgroundColor: withOpacity(surfaces.text.primary, 0.05),
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  freeButtonText: {
    fontSize: 16,
    color: surfaces.text.primary,
  },
  upgradeNote: {
    fontSize: 12,
    color: withOpacity(surfaces.text.primary, 0.5),
    textAlign: 'center',
  },
});
