{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "prepare": "husky install", "check": "tsc --noEmit && eslint . --ext .js,.jsx,.ts,.tsx && knip --no-exit-code && depcheck"}, "jest": {"preset": "jest-expo"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,md,json}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@expo-google-fonts/antonio": "^0.4.1", "@expo-google-fonts/archivo": "^0.4.1", "@expo-google-fonts/roboto": "^0.4.0", "@expo/vector-icons": "^14.1.0", "@livekit/react-native": "^2.9.1", "@livekit/react-native-webrtc": "^137.0.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.4", "@react-navigation/native": "^6.1.18", "@shopify/flash-list": "^2.0.3", "@supabase/supabase-js": "^2.56.0", "base64-arraybuffer": "^1.0.2", "dayjs": "^1.11.18", "expo": "53.0.22", "expo-av": "~15.1.7", "expo-blur": "^14.1.5", "expo-constants": "^17.1.7", "expo-dev-client": "~5.2.4", "expo-file-system": "^18.1.11", "expo-font": "~13.3.2", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "expo-web-browser": "~14.2.0", "livekit-client": "^2.15.6", "nativewind": "^2.0.11", "react": "19.0.0", "react-native": "0.79.6", "react-native-gesture-handler": "~2.24.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.3.3", "react-native-web": "^0.20.0", "tailwindcss": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.28.3", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "depcheck": "^1.4.7", "eslint": "^9.34.0", "eslint-config-prettier": "^10.1.8", "eslint-config-universe": "^15.0.3", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-unused-imports": "^4.2.0", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "~53.0.10", "knip": "^5.63.1", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "react-test-renderer": "19.0.0", "ts-prune": "^0.10.3", "typescript": "~5.8.3"}, "overrides": {"react-refresh": "~0.14.0"}, "resolutions": {"react-refresh": "~0.14.0"}, "private": true}