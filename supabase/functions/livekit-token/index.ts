import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import { AccessToken } from 'https://esm.sh/@livekit/server-sdk@1.2.7';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface TokenRequest {
  roomName: string;
  participantName: string;
  isHost?: boolean;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get Supabase configuration from environment variables
    const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
    const SUPABASE_ANON_KEY = Deno.env.get('SUPABASE_ANON_KEY');

    if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
      console.error('❌ Server configuration error: Missing Supabase environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Create Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('❌ Missing Authorization header');
      return new Response(JSON.stringify({ error: 'Unauthorized: Missing authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Verify the JWT token and get user
    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError?.message);
      return new Response(JSON.stringify({ error: 'Unauthorized: Invalid token' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    console.log(`🔐 Authenticated user: ${user.email} (${user.id})`);

    // Parse request body
    const { roomName, participantName, isHost = false }: TokenRequest = await req.json();

    console.log(
      `🔄 Generating token for: "${participantName}" in room: "${roomName}" (host: ${isHost}) - User: ${user.email}`,
    );

    // Validate input
    if (!roomName || !participantName) {
      console.error('❌ Missing required parameters');
      return new Response(JSON.stringify({ error: 'Missing roomName or participantName' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Get LiveKit configuration from environment variables
    const LIVEKIT_API_KEY = Deno.env.get('LIVEKIT_API_KEY');
    const LIVEKIT_API_SECRET = Deno.env.get('LIVEKIT_API_SECRET');
    const LIVEKIT_URL = Deno.env.get('LIVEKIT_URL');

    if (!LIVEKIT_API_KEY || !LIVEKIT_API_SECRET || !LIVEKIT_URL) {
      console.error('❌ Server configuration error: Missing LiveKit environment variables');
      return new Response(JSON.stringify({ error: 'Server configuration error' }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    // Clean and validate room name and participant name
    const safeRoomName =
      String(roomName)
        .trim()
        .replace(/[^a-zA-Z0-9\-_]/g, '')
        .toLowerCase() || 'default-room';

    const safeParticipantName =
      String(participantName)
        .trim()
        .replace(/[^a-zA-Z0-9]/g, '')
        .toLowerCase()
        .substring(0, 16) || 'user' + Math.random().toString(36).substr(2, 5);

    // Create the access token
    const at = new AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET, {
      identity: safeParticipantName,
      ttl: '10m', // Token valid for 10 minutes
    });

    // Grant permissions
    at.addGrant({
      room: safeRoomName,
      roomJoin: true,
      canPublish: isHost, // Only hosts can publish (broadcast)
      canSubscribe: true, // Everyone can listen
      canPublishData: true, // Allow sending data messages
    });

    // Generate the JWT token
    const token = at.toJwt();

    console.log(
      `✅ Token generated successfully for room: ${safeRoomName}, participant: ${safeParticipantName}`,
    );

    return new Response(JSON.stringify({ token }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('❌ Error generating token:', error);
    return new Response(JSON.stringify({ error: 'Failed to generate token' }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
