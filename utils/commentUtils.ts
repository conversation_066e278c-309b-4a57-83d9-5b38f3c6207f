import { supabase } from '../lib/supabase';

/**
 * Get the current comment count for a prayer
 */
export async function getPrayerCommentCount(prayerId: string): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('prayer_comments')
      .select('*', { count: 'exact', head: true })
      .eq('prayer_id', prayerId)
      .is('parent_id', null); // Only count top-level comments

    if (error) throw error;
    return count || 0;
  } catch (error) {
    console.error('Error getting comment count:', error);
    return 0;
  }
}

/**
 * Get comment counts for multiple prayers
 */
export async function getMultiplePrayerCommentCounts(
  prayerIds: string[],
): Promise<Record<string, number>> {
  try {
    const { data, error } = await supabase
      .from('prayer_comments')
      .select('prayer_id')
      .in('prayer_id', prayerIds)
      .is('parent_id', null); // Only count top-level comments

    if (error) throw error;

    // Count comments per prayer
    const counts: Record<string, number> = {};
    prayerIds.forEach((id) => (counts[id] = 0));

    data?.forEach((comment) => {
      counts[comment.prayer_id] = (counts[comment.prayer_id] || 0) + 1;
    });

    return counts;
  } catch (error) {
    console.error('Error getting multiple comment counts:', error);
    return {};
  }
}

/**
 * Subscribe to comment count changes for a prayer
 */
export function subscribeToCommentCount(prayerId: string, callback: (count: number) => void) {
  const subscription = supabase
    .channel(`prayer_comments:${prayerId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'prayer_comments',
        filter: `prayer_id=eq.${prayerId}`,
      },
      async () => {
        const count = await getPrayerCommentCount(prayerId);
        callback(count);
      },
    )
    .subscribe();

  return () => {
    supabase.removeChannel(subscription);
  };
}

/**
 * Format comment count for display
 */
export function formatCommentCount(count: number): string {
  if (count === 0) return 'No comments';
  if (count === 1) return '1 comment';
  if (count < 1000) return `${count} comments`;
  if (count < 1000000) return `${(count / 1000).toFixed(1)}k comments`;
  return `${(count / 1000000).toFixed(1)}m comments`;
}

/**
 * Get the total engagement (likes + comments) for a prayer
 */
export async function getPrayerEngagement(
  prayerId: string,
): Promise<{ likes: number; comments: number; total: number }> {
  try {
    const [likesResult, commentsResult] = await Promise.all([
      supabase
        .from('prayer_likes')
        .select('*', { count: 'exact', head: true })
        .eq('prayer_id', prayerId),
      supabase
        .from('prayer_comments')
        .select('*', { count: 'exact', head: true })
        .eq('prayer_id', prayerId)
        .is('parent_id', null),
    ]);

    const likes = likesResult.count || 0;
    const comments = commentsResult.count || 0;

    return {
      likes,
      comments,
      total: likes + comments,
    };
  } catch (error) {
    console.error('Error getting prayer engagement:', error);
    return { likes: 0, comments: 0, total: 0 };
  }
}
