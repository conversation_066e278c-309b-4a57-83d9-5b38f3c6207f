import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

// Extend dayjs with plugins
dayjs.extend(relativeTime);

/**
 * Format a date string to relative time (e.g., "5m ago", "2h ago", "3d ago")
 * Used for comments, prayer creation times, etc.
 */
export function formatRelativeTime(dateString: string | null): string {
  if (!dateString) return '';
  
  const date = dayjs(dateString);
  const now = dayjs();
  const diffInMinutes = now.diff(date, 'minute');
  const diffInHours = now.diff(date, 'hour');
  const diffInDays = now.diff(date, 'day');

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  } else {
    // For older dates, show absolute date
    return date.format('MMM D, YYYY');
  }
}

/**
 * Format a date string to absolute date (e.g., "Jan 15, 2024")
 * Used for older dates or when absolute time is needed
 */
export function formatAbsoluteDate(dateString: string | null): string {
  if (!dateString) return '';
  return dayjs(dateString).format('MMM D, YYYY');
}

/**
 * Format a date string to date and time (e.g., "Jan 15, 2024 at 3:30 PM")
 * Used for scheduled prayers or detailed timestamps
 */
export function formatDateTime(dateString: string | null): string {
  if (!dateString) return '';
  return dayjs(dateString).format('MMM D, YYYY [at] h:mm A');
}

/**
 * Format a date string to time only (e.g., "3:30 PM")
 * Used for time pickers and time-only displays
 */
export function formatTime(dateString: string | null): string {
  if (!dateString) return '';
  return dayjs(dateString).format('h:mm A');
}

/**
 * Format a Date object for the create screen date picker
 * Used in create prayer form
 */
export function formatScheduledDate(date: Date | null): string {
  if (!date || isNaN(date.getTime())) {
    return 'Select Date';
  }
  return dayjs(date).format('ddd, MMM D, YYYY');
}

/**
 * Format a Date object for the create screen time picker
 * Used in create prayer form
 */
export function formatScheduledTime(date: Date | null): string {
  if (!date || isNaN(date.getTime())) {
    return 'Select Time';
  }
  return dayjs(date).format('h:mm A');
}

/**
 * Check if a date is today
 */
export function isToday(dateString: string | null): boolean {
  if (!dateString) return false;
  return dayjs(dateString).isSame(dayjs(), 'day');
}

/**
 * Check if a date is this week
 */
export function isThisWeek(dateString: string | null): boolean {
  if (!dateString) return false;
  return dayjs(dateString).isSame(dayjs(), 'week');
}

/**
 * Get a human-readable duration (e.g., "2 hours", "30 minutes")
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} hour${hours !== 1 ? 's' : ''}`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
}
