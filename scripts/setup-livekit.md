# LiveKit Setup Guide

## Step 1: Get LiveKit Credentials

1. Go to [LiveKit Cloud](https://cloud.livekit.io) and create an account
2. Create a new project or use an existing one
3. Copy your:
   - **WebSocket URL** (e.g., `wss://your-project.livekit.cloud`)
   - **API Key**
   - **API Secret**

## Step 2: Configure Environment Variables

Copy the example environment file and add your LiveKit WebSocket URL:

```bash
# Copy the template
cp env.example .env
```

Then edit `.env` with **only** your LiveKit WebSocket URL:

```env
# LiveKit Configuration - ONLY the WebSocket URL goes here
EXPO_PUBLIC_LIVEKIT_URL=wss://your-project.livekit.cloud
```

**🔒 Security Note**: Do NOT put API_KEY and API_SECRET in the `.env` file! Those should only be stored as Supabase secrets (Step 3).

## Step 3: Set Supabase Edge Function Secrets

Run these commands to set the LiveKit credentials for the Edge Function:

```bash
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase (if not already logged in)
npx supabase login

# Set the LiveKit secrets for the Edge Function
npx supabase secrets set LIVEKIT_API_KEY=your-api-key --project-ref sbtpfbqjsuhguklhfgzz
npx supabase secrets set LIVEKIT_API_SECRET=your-api-secret --project-ref sbtpfbqjsuhguklhfgzz
npx supabase secrets set LIVEKIT_URL=wss://your-project.livekit.cloud --project-ref sbtpfbqjsuhguklhfgzz
```

## Step 4: Test the Setup

1. Build your development app (if using Expo Go, this won't work):
   ```bash
   npx expo prebuild --clean
   npx expo run:ios
   # or
   npx expo run:android
   ```

# LiveKit Configuration

EXPO_PUBLIC_LIVEKIT_URL=wss://thepowerofmany-iaoecodk.livekit.cloud
EXPO_PUBLIC_LIVEKIT_API_KEY=API59caHN4XQMrX
EXPO_PUBLIC_LIVEKIT_API_SECRET=e4R6QVJndXdkOl8ihFHdNkJYiEMeLqY611Ak911H83G

2. Create a prayer and try broadcasting
3. Check the console for the success message: "✅ Generated LiveKit token successfully"

## Troubleshooting

### Error: "Server configuration error"

- Make sure you've set all three Supabase secrets (API_KEY, API_SECRET, URL)
- Verify the secret names are exactly: `LIVEKIT_API_KEY`, `LIVEKIT_API_SECRET`, `LIVEKIT_URL`

### Error: "No authorization header"

- Make sure you're logged in to the app
- The user must be authenticated to generate tokens

### Error: "Failed to generate token"

- Check that your LiveKit credentials are valid
- Verify the LiveKit URL format (should start with `wss://`)

### Still getting mock token warning?

- Make sure you've restarted your app after setting environment variables
- Clear Metro cache: `npx expo start -c`

## Development vs Production

### Development

- Use the `.env` file for client-side configuration
- Tokens are generated via Supabase Edge Function

### Production

- Set environment variables in your deployment platform
- The Edge Function automatically uses the secrets you set
