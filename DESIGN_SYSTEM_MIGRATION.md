# Design System Migration Guide

## Overview

This guide shows how to migrate from the old inconsistent styling to the new unified design system.

## New Design System Structure

### 1. Core System (`lib/design/system.ts`)

- **designTokens**: All base values (spacing, colors, typography, etc.)
- **surfaces**: Dark theme surface colors and text colors
- **withOpacity**: Helper function for rgba colors

### 2. Component Styles (`lib/design/components.ts`)

- **componentStyles**: Pre-built component styles (cards, buttons, chips, etc.)
- **textStyles**: Typography styles (headings, body text, etc.)

### 3. Main Export (`lib/design/index.ts`)

- **useDesignSystem**: Hook to access everything
- **commonStyles**: Pre-combined style arrays
- **Helper functions**: getStatusColor, getStatusBadgeStyle, etc.

## Migration Examples

### Before (Inconsistent):

```tsx
// Old way - different styles everywhere
const styles = StyleSheet.create({
  card: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginBottom: 8,
  },
});
```

### After (Consistent):

```tsx
// New way - using design system
import { componentStyles, textStyles, spacing } from '../lib/design';

const styles = StyleSheet.create({
  card: {
    ...componentStyles.card,
    ...componentStyles.mb_md,
  },
  title: {
    ...textStyles.h4,
    ...componentStyles.mb_sm,
  },
});

// Or even better, use pre-combined styles:
import { commonStyles } from '../lib/design';

const styles = StyleSheet.create({
  card: commonStyles.prayerCard,
  title: commonStyles.cardTitle,
});
```

## Key Benefits

### 1. Consistency

- All cards use the same padding, border radius, colors
- All buttons have consistent sizing and spacing
- Typography is unified across the app

### 2. Maintainability

- Change colors in one place, updates everywhere
- Easy to add new variants
- Type-safe with TypeScript

### 3. Performance

- Pre-computed StyleSheet objects
- No runtime style calculations
- Optimized for React Native

## Component Usage Examples

### Cards

```tsx
import { Card } from '../components/ui/Card';

// Basic card
<Card>
  <Text>Content</Text>
</Card>

// Elevated card with custom style
<Card variant="elevated" style={{ marginTop: 16 }}>
  <Text>Content</Text>
</Card>
```

### Buttons

```tsx
import { Button } from '../components/ui/Button';

// Primary button
<Button title="Save" onPress={handleSave} />

// Secondary button
<Button title="Cancel" variant="secondary" onPress={handleCancel} />

// Small success button
<Button title="Done" variant="success" size="small" onPress={handleDone} />
```

### Badges

```tsx
import { Badge } from '../components/ui/Badge';

// Category badge
<Badge text="Health & Healing" variant="category" />

// Live badge
<Badge text="LIVE" variant="live" />

// Success badge
<Badge text="Answered" variant="success" />
```

### Chips/Filters

```tsx
import { Chip } from '../components/ui/Chip';

// Filter chip
<Chip
  text="All"
  active={activeFilter === 'all'}
  onPress={() => setActiveFilter('all')}
/>

// Category chip
<Chip
  text="Health"
  active={activeCategory === 'health'}
  variant="secondary"
  onPress={() => setActiveCategory('health')}
/>
```

## Migration Steps

### 1. Update Imports

```tsx
// Old
import { spacing, radius } from '../constants/design';
import { useDesignTokens, createStyles } from '../lib/design';

// New
import { componentStyles, textStyles, spacing, radius, useDesignSystem } from '../lib/design';
```

### 2. Replace Custom Styles

```tsx
// Old
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0a0a0a',
    paddingHorizontal: 16,
  },
  card: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    padding: 24,
    borderRadius: 12,
    marginBottom: 16,
  },
});

// New
const styles = StyleSheet.create({
  container: {
    ...componentStyles.container,
    ...componentStyles.containerPadded,
  },
  card: {
    ...componentStyles.card,
    ...componentStyles.mb_md,
  },
});
```

### 3. Use Pre-built Components

Replace custom styled components with the new UI components:

- Replace custom cards with `<Card>`
- Replace custom buttons with `<Button>`
- Replace custom badges with `<Badge>`
- Replace custom chips with `<Chip>`

## Color System

### Status Colors

```tsx
import { getStatusColor } from '../lib/design';

const liveColor = getStatusColor('live'); // #2dd4bf
const successColor = getStatusColor('success'); // #4ade80
const errorColor = getStatusColor('error'); // #f87171
```

### Surface Colors

```tsx
import { surfaces } from '../lib/design';

const cardBackground = surfaces.card; // rgba(255,255,255,0.05)
const borderColor = surfaces.border; // rgba(255,255,255,0.1)
const primaryText = surfaces.text.primary; // #ffffff
const secondaryText = surfaces.text.secondary; // rgba(255,255,255,0.8)
```

## Spacing System (8px base)

```tsx
import { spacing } from '../lib/design';

const styles = StyleSheet.create({
  container: {
    padding: spacing.md, // 16px
    marginBottom: spacing.lg, // 24px
    gap: spacing.sm, // 8px
  },
});

// Or use utility classes
const styles = StyleSheet.create({
  container: {
    ...componentStyles.p_md,
    ...componentStyles.mb_lg,
    ...componentStyles.gap_sm,
  },
});
```

## Next Steps

1. Start with the home screen (index.tsx)
2. Replace filter chips with new Chip component
3. Replace prayer cards with new Card component
4. Update all buttons to use Button component
5. Gradually migrate other screens

This will ensure consistent spacing, colors, and typography across the entire app!
