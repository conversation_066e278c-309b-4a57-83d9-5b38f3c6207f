// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname, {
  // [Web-only]: Enables CSS support in Metro.
  isCSSEnabled: true,
});

// Add resolver configuration for LiveKit WebRTC
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-webrtc': require.resolve('@livekit/react-native-webrtc'),
};

config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;
