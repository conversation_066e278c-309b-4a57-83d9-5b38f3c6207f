import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import type { Database } from './supabase.generated';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});




// Type aliases for easier use
export type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
export type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
export type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];

export type Prayer = Database['public']['Tables']['prayers']['Row'];
export type PrayerInsert = Database['public']['Tables']['prayers']['Insert'];
export type PrayerUpdate = Database['public']['Tables']['prayers']['Update'];

export type PrayerOutcome = Database['public']['Tables']['prayer_outcomes']['Row'];
export type PrayerOutcomeInsert = Database['public']['Tables']['prayer_outcomes']['Insert'];
export type PrayerOutcomeUpdate = Database['public']['Tables']['prayer_outcomes']['Update'];

export type PrayerComment = Database['public']['Tables']['prayer_comments']['Row'];
export type PrayerCommentInsert = Database['public']['Tables']['prayer_comments']['Insert'];
export type PrayerCommentUpdate = Database['public']['Tables']['prayer_comments']['Update'];

export type CommentReport = Database['public']['Tables']['comment_reports']['Row'];
export type CommentReportInsert = Database['public']['Tables']['comment_reports']['Insert'];
export type CommentReportUpdate = Database['public']['Tables']['comment_reports']['Update'];

// Report reasons enum
export type ReportReason = 'spam' | 'inappropriate' | 'harassment' | 'false_information' | 'other';

// Extended types with relationships
export interface PrayerWithProfile extends Prayer {
  user_profiles?: UserProfile;
  prayer_outcomes?: PrayerOutcome;
  joined_count?: number; // Count of users who joined this prayer
}

export interface PrayerCommentWithProfile extends PrayerComment {
  user_profiles?: UserProfile;
  replies?: PrayerCommentWithProfile[]; // For nested replies
}
