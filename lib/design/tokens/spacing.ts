// Spacing tokens for the prayer app design system
// 8px-based spacing system for consistent layouts

export interface SpacingTokens {
  xs: number; // 4px  - 0.5x
  sm: number; // 8px  - 1x (base)
  md: number; // 16px - 2x
  lg: number; // 24px - 3x
  xl: number; // 32px - 4x
  '2xl': number; // 40px - 5x
  '3xl': number; // 48px - 6x
  '4xl': number; // 64px - 8x
  '5xl': number; // 80px - 10x
  '6xl': number; // 96px - 12x
}

export interface RadiusTokens {
  xs: number; // 4px
  sm: number; // 8px  - Primary radius
  md: number; // 8px  - Keep consistent
  lg: number; // 8px  - Keep consistent
  xl: number; // 12px - Slightly larger for special cases
  '2xl': number; // 16px - For large components
  full: number; // 9999px - Fully rounded
}

// Main spacing scale
export const spacing: SpacingTokens = {
  xs: 4, // 0.5x
  sm: 8, // 1x (base unit)
  md: 16, // 2x
  lg: 24, // 3x
  xl: 32, // 4x
  '2xl': 40, // 5x
  '3xl': 48, // 6x
  '4xl': 64, // 8x
  '5xl': 80, // 10x
  '6xl': 96, // 12x
};

// Border radius scale (small throughout per user preference)
export const radius: RadiusTokens = {
  xs: 4,
  sm: 8, // Primary radius used throughout
  md: 8, // Keep consistent with sm
  lg: 8, // Keep consistent with sm
  xl: 12, // Slightly larger for special cases
  '2xl': 16, // For large components like modals
  full: 9999, // Fully rounded (pills, circles)
};

// Component-specific spacing
export const componentSpacing = {
  // Button padding
  button: {
    sm: {
      paddingVertical: spacing.sm, // 8px
      paddingHorizontal: spacing.md, // 16px
    },
    md: {
      paddingVertical: spacing.md, // 16px
      paddingHorizontal: spacing.lg, // 24px
    },
    lg: {
      paddingVertical: spacing.lg, // 24px
      paddingHorizontal: spacing.xl, // 32px
    },
  },

  // Input padding
  input: {
    paddingVertical: spacing.md, // 16px
    paddingHorizontal: spacing.lg, // 24px
  },

  // Card padding
  card: {
    sm: spacing.md, // 16px
    md: spacing.lg, // 24px
    lg: spacing.xl, // 32px
  },

  // Modal padding
  modal: {
    padding: spacing.lg, // 24px
    margin: spacing.md, // 16px
  },

  // List item spacing
  listItem: {
    paddingVertical: spacing.md, // 16px
    paddingHorizontal: spacing.lg, // 24px
    gap: spacing.sm, // 8px
  },

  // Icon sizes
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 28,
    '2xl': 32,
    '3xl': 40,
    '4xl': 48,
  },

  // Avatar sizes
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
    '2xl': 80,
    '3xl': 96,
  },
};

// Layout spacing helpers
export const layout = {
  // Screen padding
  screenPadding: {
    horizontal: spacing.md, // 16px
    vertical: spacing.lg, // 24px
  },

  // Section spacing
  section: {
    marginBottom: spacing['2xl'], // 40px
  },

  // Content spacing
  content: {
    gap: spacing.lg, // 24px between major elements
    itemGap: spacing.md, // 16px between related items
    textGap: spacing.sm, // 8px between text elements
  },

  // Header spacing
  header: {
    paddingTop: 120, // Account for status bar + custom header
    marginBottom: spacing.xl, // 32px
  },

  // Bottom safe area
  bottomSafeArea: 100, // Space for tab bar + safe area
};

// Utility functions
export const getSpacing = (multiplier: number): number => {
  return spacing.sm * multiplier; // Base 8px * multiplier
};

export const getComponentSpacing = (component: keyof typeof componentSpacing, size?: string) => {
  const comp = componentSpacing[component];
  if (typeof comp === 'object' && size && size in comp) {
    return comp[size as keyof typeof comp];
  }
  return comp;
};

// Export spacing values as both numbers and strings for different use cases
export const spacingStrings = Object.fromEntries(
  Object.entries(spacing).map(([key, value]) => [key, `${value}px`]),
) as Record<keyof SpacingTokens, string>;

export const radiusStrings = Object.fromEntries(
  Object.entries(radius).map(([key, value]) => [key, `${value}px`]),
) as Record<keyof RadiusTokens, string>;

// Type exports
export type SpacingKey = keyof SpacingTokens;
export type RadiusKey = keyof RadiusTokens;
export type ComponentSpacingKey = keyof typeof componentSpacing;
export type IconSize = keyof typeof componentSpacing.icon;
export type AvatarSize = keyof typeof componentSpacing.avatar;
