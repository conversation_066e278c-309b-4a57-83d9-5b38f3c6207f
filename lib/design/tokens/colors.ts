// Color tokens for the prayer app design system
// Provides both hex values for React Native and semantic color organization

export interface ColorScale {
  50: string;
  100: string;
  200: string;
  300: string;
  400: string;
  500: string; // Base color
  600: string;
  700: string;
  800: string;
  900: string;
}

export interface ColorVariants {
  primary: string;
  secondary: string;
  tertiary: string;
  elevated: string;
}

export interface TextColorVariants {
  primary: string;
  secondary: string;
  tertiary: string;
  quaternary: string;
  disabled: string;
}

export interface PrayerColorVariants {
  background: string;
  border: string;
  text: string;
  accent: string;
}

export interface SurfaceColors {
  glassLight: string;
  glassMedium: string;
  glassStrong: string;
  glassBorder: string;
}

export interface ColorTokens {
  // Primary palette (Purple/Blue theme)
  primary: ColorScale;

  // Semantic colors
  success: ColorScale;
  error: ColorScale;
  warning: ColorScale;
  info: ColorScale;

  // Prayer-specific colors
  prayer: ColorScale;

  // Surface colors for glassmorphism
  surface: SurfaceColors;

  // Text colors
  text: TextColorVariants;

  // Background variants
  background: ColorVariants;
}

// Main color definitions
export const colors: ColorTokens = {
  primary: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#8b5cf6', // Main purple
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },

  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e', // Main green
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444', // Main red
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Main orange
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6', // Main blue
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  prayer: {
    50: '#fdf2f8',
    100: '#fce7f3',
    200: '#fbcfe8',
    300: '#f9a8d4',
    400: '#f472b6',
    500: '#ec4899', // Main prayer pink
    600: '#db2777',
    700: '#be185d',
    800: '#9d174d',
    900: '#831843',
  },

  surface: {
    glassLight: 'rgba(255, 255, 255, 0.12)', // Enhanced from 0.05 to 0.12
    glassMedium: 'rgba(255, 255, 255, 0.25)', // Enhanced from 0.15 to 0.25
    glassStrong: 'rgba(255, 255, 255, 0.32)', // Enhanced from 0.2 to 0.32
    glassBorder: 'rgba(255, 255, 255, 0.4)', // Enhanced from 0.3 to 0.4
  },

  text: {
    primary: '#ffffff',
    secondary: 'rgba(255, 255, 255, 0.8)',
    tertiary: 'rgba(255, 255, 255, 0.6)',
    quaternary: 'rgba(255, 255, 255, 0.4)',
    disabled: 'rgba(255, 255, 255, 0.3)',
  },

  // Card-specific text colors for bright white glassmorphism cards
  cardText: {
    primary: 'rgba(0, 0, 0, 0.9)', // Dark text for bright white cards
    secondary: 'rgba(0, 0, 0, 0.7)', // Medium dark text
    tertiary: 'rgba(0, 0, 0, 0.5)', // Light dark text
    disabled: 'rgba(0, 0, 0, 0.3)', // Very light dark text
  },

  background: {
    primary: '#000000',
    secondary: 'rgba(0, 0, 0, 0.8)',
    tertiary: 'rgba(0, 0, 0, 0.6)',
    elevated: 'rgba(255, 255, 255, 0.05)',
  },
};

// Utility functions for color manipulation
export const withOpacity = (color: string, opacity: number): string => {
  // Convert hex to rgba if needed
  if (color.startsWith('#')) {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // If already rgba, replace the alpha value
  if (color.startsWith('rgba')) {
    return color.replace(/[\d\.]+\)$/g, `${opacity})`);
  }

  return color;
};

// Semantic color helpers
export const semanticColors = {
  // Button variants
  button: {
    primary: {
      background: withOpacity(colors.success[500], 0.2),
      border: withOpacity(colors.success[500], 0.3),
      text: colors.text.primary,
    },
    secondary: {
      background: colors.surface.glassMedium, // Now uses enhanced 0.25 opacity
      border: colors.surface.glassBorder, // Now uses enhanced 0.4 opacity
      text: colors.text.primary,
    },
    destructive: {
      background: withOpacity(colors.error[500], 0.18),
      border: withOpacity(colors.error[500], 0.28),
      text: colors.text.primary,
    },
    prayer: {
      background: withOpacity(colors.prayer[500], 0.2),
      border: withOpacity(colors.prayer[500], 0.3),
      text: colors.text.primary,
    },
  },

  // Status colors
  status: {
    live: {
      background: withOpacity(colors.error[500], 0.2),
      border: withOpacity(colors.error[500], 0.3),
      text: colors.error[300],
      dot: colors.error[500],
    },
    answered: {
      background: withOpacity(colors.success[500], 0.2),
      border: withOpacity(colors.success[500], 0.3),
      text: colors.success[300],
    },
    scheduled: {
      background: withOpacity(colors.primary[500], 0.2),
      border: withOpacity(colors.primary[500], 0.3),
      text: colors.primary[300],
    },
  },

  // Card surfaces
  card: {
    background: colors.surface.glassLight, // Now uses enhanced 0.12 opacity
    border: colors.surface.glassMedium, // Now uses enhanced 0.25 opacity
    elevated: colors.surface.glassMedium, // Now uses enhanced 0.25 opacity
  },
};
