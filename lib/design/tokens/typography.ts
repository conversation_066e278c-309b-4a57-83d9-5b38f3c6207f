// Typography tokens for the prayer app design system
// Consistent text styles and font hierarchy

export interface FontWeight {
  light: '300';
  regular: '400';
  medium: '500';
  semibold: '600';
  bold: '700';
}

export interface FontSize {
  xs: number; // 12px
  sm: number; // 14px
  base: number; // 16px
  lg: number; // 18px
  xl: number; // 20px
  '2xl': number; // 24px
  '3xl': number; // 30px
  '4xl': number; // 36px
}

export interface LineHeight {
  xs: number; // 16px
  sm: number; // 20px
  base: number; // 24px
  lg: number; // 28px
  xl: number; // 28px
  '2xl': number; // 32px
  '3xl': number; // 36px
  '4xl': number; // 40px
}

export interface TypographyStyle {
  fontSize: number;
  lineHeight: number;
  fontWeight: '300' | '400' | '500' | '600' | '700';
  fontFamily: string;
  letterSpacing?: number;
}

// Font families (matching your existing fonts)
export const fontFamily = {
  roboto: {
    light: 'Roboto-Light',
    regular: 'Roboto-Regular',
    medium: 'Roboto-Medium',
    semibold: 'Roboto-SemiBold',
    bold: 'Roboto-Bold',
  },
  archivo: {
    regular: 'Archivo-Regular',
    medium: 'Archivo-Medium',
    semibold: 'Archivo-SemiBold',
    bold: 'Archivo-Bold',
  },
  antonio: {
    light: 'Antonio-Light',
    regular: 'Antonio-Regular',
    medium: 'Antonio-Medium',
    semibold: 'Antonio-SemiBold',
    bold: 'Antonio-Bold',
  },
};

// Font weights
export const fontWeight: FontWeight = {
  light: '300',
  regular: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
};

// Font sizes
export const fontSize: FontSize = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
};

// Line heights
export const lineHeight: LineHeight = {
  xs: 16,
  sm: 20,
  base: 24,
  lg: 28,
  xl: 28,
  '2xl': 32,
  '3xl': 36,
  '4xl': 40,
};

// Typography scale with semantic naming
export const typography = {
  // Display text (large headings)
  display: {
    large: {
      fontSize: fontSize['4xl'],
      lineHeight: lineHeight['4xl'],
      fontWeight: fontWeight.light,
      fontFamily: fontFamily.roboto.light,
      letterSpacing: -0.5,
    } as TypographyStyle,
    medium: {
      fontSize: fontSize['3xl'],
      lineHeight: lineHeight['3xl'],
      fontWeight: fontWeight.light,
      fontFamily: fontFamily.roboto.light,
      letterSpacing: -0.5,
    } as TypographyStyle,
    small: {
      fontSize: fontSize['2xl'],
      lineHeight: lineHeight['2xl'],
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.archivo.medium,
      letterSpacing: -0.3,
    } as TypographyStyle,
  },

  // Headings
  heading: {
    h1: {
      fontSize: fontSize['3xl'], // 30
      lineHeight: lineHeight['3xl'],
      fontWeight: fontWeight.light, // '300'
      fontFamily: fontFamily.roboto.light, // Roboto-Light
      letterSpacing: -0.5,
    } as TypographyStyle,
    h2: {
      fontSize: fontSize['3xl'], // 30
      lineHeight: lineHeight['3xl'],
      fontWeight: fontWeight.light,
      fontFamily: fontFamily.roboto.light,
      letterSpacing: -0.5,
    } as TypographyStyle,
    h3: {
      fontSize: fontSize['3xl'], // 30
      lineHeight: lineHeight['3xl'],
      fontWeight: fontWeight.light,
      fontFamily: fontFamily.roboto.light,
      letterSpacing: -0.5,
    } as TypographyStyle,
    h4: {
      fontSize: fontSize['3xl'], // 30
      lineHeight: lineHeight['3xl'],
      fontWeight: fontWeight.light,
      fontFamily: fontFamily.roboto.light,
      letterSpacing: -0.5,
    } as TypographyStyle,
  },

  // Body text
  body: {
    large: {
      fontSize: fontSize.lg,
      lineHeight: lineHeight.lg,
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
    medium: {
      fontSize: fontSize.base,
      lineHeight: lineHeight.base,
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
    small: {
      fontSize: fontSize.sm,
      lineHeight: lineHeight.sm,
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
  },

  // Labels and UI text
  label: {
    large: {
      fontSize: fontSize.base,
      lineHeight: lineHeight.base,
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.roboto.medium,
    } as TypographyStyle,
    medium: {
      fontSize: fontSize.sm,
      lineHeight: lineHeight.sm,
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.roboto.medium,
    } as TypographyStyle,
    small: {
      fontSize: fontSize.xs,
      lineHeight: lineHeight.xs,
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.roboto.medium,
    } as TypographyStyle,
  },

  // Button text
  button: {
    large: {
      fontSize: fontSize.lg,
      lineHeight: lineHeight.lg,
      fontWeight: fontWeight.semibold,
      fontFamily: fontFamily.archivo.semibold,
    } as TypographyStyle,
    medium: {
      fontSize: fontSize.base,
      lineHeight: lineHeight.base,
      fontWeight: fontWeight.semibold,
      fontFamily: fontFamily.archivo.semibold,
    } as TypographyStyle,
    small: {
      fontSize: fontSize.sm,
      lineHeight: lineHeight.sm,
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.roboto.medium,
    } as TypographyStyle,
  },

  // Caption and helper text
  caption: {
    large: {
      fontSize: fontSize.sm,
      lineHeight: lineHeight.sm,
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
    medium: {
      fontSize: fontSize.xs,
      lineHeight: lineHeight.xs,
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
  },

  // Prayer-specific text styles
  prayer: {
    title: {
      fontSize: fontSize.xl,
      lineHeight: lineHeight.xl,
      fontWeight: fontWeight.semibold,
      fontFamily: fontFamily.roboto.medium,
      letterSpacing: -0.3,
    } as TypographyStyle,
    text: {
      fontSize: fontSize.base,
      lineHeight: lineHeight.lg, // Slightly more line height for readability
      fontWeight: fontWeight.regular,
      fontFamily: fontFamily.roboto.regular,
    } as TypographyStyle,
    host: {
      fontSize: fontSize.sm,
      lineHeight: lineHeight.sm,
      fontWeight: fontWeight.medium,
      fontFamily: fontFamily.roboto.medium,
    } as TypographyStyle,
  },
};

// Utility functions
export const getTypographyStyle = (
  category: keyof typeof typography,
  variant: string,
): TypographyStyle => {
  const categoryStyles = typography[category];
  if (variant in categoryStyles) {
    return categoryStyles[variant as keyof typeof categoryStyles];
  }
  // Fallback to first available style
  const firstKey = Object.keys(categoryStyles)[0];
  return categoryStyles[firstKey as keyof typeof categoryStyles];
};

// Export types
export type TypographyCategory = keyof typeof typography;
export type FontFamilyKey = keyof typeof fontFamily;
export type FontSizeKey = keyof FontSize;
export type LineHeightKey = keyof LineHeight;
