// Effects tokens for the prayer app design system
// Shadows, blur effects, and visual enhancements

export interface ShadowStyle {
  shadowColor: string;
  shadowOffset: { width: number; height: number };
  shadowOpacity: number;
  shadowRadius: number;
  elevation: number; // Android
}

export interface BlurIntensity {
  light: number; // 16
  medium: number; // 24
  strong: number; // 40
  intense: number; // 60
  maximum: number; // 80
}

export interface GradientColors {
  colors: string[];
  locations?: number[];
}

// Blur intensities (standardized from your current usage)
export const blur: BlurIntensity = {
  light: 25, // For subtle effects
  medium: 40, // For cards and modals (updated to stronger blur)
  strong: 55, // For prominent elements
  intense: 70, // For overlays
  maximum: 90, // For strong separation
};

// Shadow definitions
export const shadows = {
  // Subtle shadows
  xs: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  } as ShadowStyle,

  sm: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  } as ShadowStyle,

  md: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  } as ShadowStyle,

  lg: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  } as ShadowStyle,

  xl: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  } as ShadowStyle,

  // Colored shadows for specific elements
  prayer: {
    shadowColor: '#8b5cf6', // Purple
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  } as ShadowStyle,

  success: {
    shadowColor: '#22c55e', // Green
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  } as ShadowStyle,

  error: {
    shadowColor: '#ef4444', // Red
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  } as ShadowStyle,

  info: {
    shadowColor: '#3b82f6', // Blue
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  } as ShadowStyle,
};

// Gradient definitions
export const gradients = {
  // Background gradients
  background: {
    primary: {
      colors: ['rgba(0,0,0,0.9)', 'rgba(0,0,0,0.7)', 'rgba(0,0,0,0.8)'],
    } as GradientColors,
    prayer: {
      colors: ['rgba(139, 92, 246, 0.1)', 'rgba(0,0,0,0.8)', 'rgba(236, 72, 153, 0.1)'],
    } as GradientColors,
  },

  // Button gradients
  button: {
    primary: {
      colors: ['rgba(34, 197, 94, 0.3)', 'rgba(34, 197, 94, 0.2)'],
    } as GradientColors,
    prayer: {
      colors: ['rgba(139, 92, 246, 0.3)', 'rgba(236, 72, 153, 0.2)'],
    } as GradientColors,
    broadcast: {
      colors: ['rgba(0, 122, 255, 0.8)', 'rgba(0, 122, 255, 0.6)'],
    } as GradientColors,
    broadcastActive: {
      colors: ['rgba(239, 68, 68, 0.8)', 'rgba(239, 68, 68, 0.6)'],
    } as GradientColors,
  },

  // Overlay gradients
  overlay: {
    modal: {
      colors: ['rgba(0,0,0,0.4)', 'rgba(0,0,0,0.6)'],
    } as GradientColors,
    card: {
      colors: ['rgba(255,255,255,0.05)', 'rgba(255,255,255,0.02)'],
    } as GradientColors,
  },
};

// Animation configurations
export const animations = {
  // Duration presets
  duration: {
    fast: 200,
    normal: 300,
    slow: 500,
    slower: 800,
  },

  // Easing curves
  easing: {
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
    spring: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // Common animation configs
  fadeIn: {
    duration: 300,
    easing: 'ease-out',
  },

  slideUp: {
    duration: 400,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  scale: {
    duration: 200,
    easing: 'ease-out',
  },
};

// Glass morphism effect helper
export const glassMorphism = {
  light: {
    backgroundColor: 'rgba(255, 255, 255, 0.12)', // Enhanced from 0.05 to 0.12
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)', // Enhanced from 0.1 to 0.2
    backdropFilter: 'blur(25px)', // Enhanced from 16px to 25px
  },

  medium: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)', // Enhanced from 0.15 to 0.25
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.4)', // Enhanced from 0.3 to 0.4
    backdropFilter: 'blur(40px)', // Enhanced from 28px to 40px
  },

  strong: {
    backgroundColor: 'rgba(255, 255, 255, 0.32)', // Enhanced from 0.2 to 0.32
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)', // Enhanced from 0.4 to 0.5
    backdropFilter: 'blur(55px)', // Enhanced from 40px to 55px
  },
};

// Utility functions
export const createShadow = (
  color: string = '#000000',
  offset: { width: number; height: number } = { width: 0, height: 4 },
  opacity: number = 0.15,
  radius: number = 8,
  elevation: number = 4,
): ShadowStyle => ({
  shadowColor: color,
  shadowOffset: offset,
  shadowOpacity: opacity,
  shadowRadius: radius,
  elevation,
});

export const createGradient = (colors: string[], locations?: number[]): GradientColors => ({
  colors,
  locations,
});

// Export types
export type ShadowKey = keyof typeof shadows;
export type BlurKey = keyof BlurIntensity;
export type GradientKey = keyof typeof gradients;
export type AnimationDuration = keyof typeof animations.duration;
export type AnimationEasing = keyof typeof animations.easing;
