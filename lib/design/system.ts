import { TextStyle } from 'react-native';

// Base design tokens - single source of truth
export const designTokens = {
  // Spacing system (8px base)
  spacing: {
    xs: 4, // 0.5x
    sm: 8, // 1x
    md: 16, // 2x
    lg: 24, // 3x
    xl: 32, // 4x
    xxl: 48, // 6x
  },

  // Border radius system (8px throughout per design preferences)
  radius: {
    xs: 4,
    sm: 8,
    md: 8, // Keep consistent with 8px system
    lg: 8, // Keep consistent with 8px system
    xl: 8, // Keep consistent with 8px system
    full: 9999,
  },

  // Typography system
  typography: {
    // Font sizes
    fontSize: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      xxl: 24,
      xxxl: 32,
    },
    // Font weights
    fontWeight: {
      normal: '400' as TextStyle['fontWeight'],
      medium: '500' as TextStyle['fontWeight'],
      semibold: '600' as TextStyle['fontWeight'],
      bold: '700' as TextStyle['fontWeight'],
    },
    // Line heights
    lineHeight: {
      tight: 1.2,
      normal: 1.4,
      relaxed: 1.6,
    },
  },

  // Color system
  colors: {
    // Base colors
    white: '#ffffff',
    black: '#000000',
    transparent: 'transparent',

    // Gray scale
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },

    // Primary (Purple)
    primary: {
      50: '#faf5ff',
      100: '#f3e8ff',
      200: '#e9d5ff',
      300: '#d8b4fe',
      400: '#c084fc',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7c3aed',
      800: '#6b21a8',
      900: '#581c87',
    },

    // Secondary (Pink)
    secondary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#ec4899',
      600: '#db2777',
      700: '#be185d',
      800: '#9d174d',
      900: '#831843',
    },

    // Success (Green)
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
    },

    // Error (Red)
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444',
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
    },

    // Warning (Yellow)
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b',
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
    },

    // Info (Blue)
    info: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },

    // Live/Broadcast (Teal)
    live: {
      50: '#f0fdfa',
      100: '#ccfbf1',
      200: '#99f6e4',
      300: '#5eead4',
      400: '#2dd4bf',
      500: '#14b8a6',
      600: '#0d9488',
      700: '#0f766e',
      800: '#115e59',
      900: '#134e4a',
    },
  },

  // Shadow system
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 6,
      elevation: 3,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.15,
      shadowRadius: 15,
      elevation: 6,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 20 },
      shadowOpacity: 0.25,
      shadowRadius: 25,
      elevation: 10,
    },
  },

  // Opacity levels
  opacity: {
    disabled: 0.4,
    subtle: 0.6,
    medium: 0.8,
    high: 0.9,
  },
};

// Helper function to create rgba colors
export const withOpacity = (color: string, opacity: number): string => {
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  return color;
};

// Surface colors for dark theme with glassmorphism
export const surfaces = {
  background: '#0a0a0a',
  // Glassmorphism surfaces - centralized for consistency (ALL CARDS USE THESE)
  card: withOpacity(designTokens.colors.white, 0.25), // Standard card background - Enhanced to 0.25 for more prominent white
  cardHover: withOpacity(designTokens.colors.white, 0.35), // Hover state - Enhanced to 0.35
  cardActive: withOpacity(designTokens.colors.white, 0.45), // Active/pressed state - Enhanced to 0.45
  border: withOpacity(designTokens.colors.white, 0.3), // Standard border - Enhanced to 0.3 for better visibility
  borderHover: withOpacity(designTokens.colors.white, 0.4), // Enhanced to 0.4
  borderActive: withOpacity(designTokens.colors.white, 0.5), // Enhanced to 0.5
  text: {
    primary: designTokens.colors.white,
    secondary: withOpacity(designTokens.colors.white, 0.8),
    tertiary: withOpacity(designTokens.colors.white, 0.6),
    disabled: withOpacity(designTokens.colors.white, 0.4),
  },
  // Card-specific text colors for better contrast against bright white glassmorphism
  cardText: {
    primary: withOpacity(designTokens.colors.black, 0.9), // Dark text for bright white cards
    secondary: withOpacity(designTokens.colors.black, 0.7), // Medium dark text
    tertiary: withOpacity(designTokens.colors.black, 0.5), // Light dark text
    disabled: withOpacity(designTokens.colors.black, 0.3), // Very light dark text
  },
  // Additional glassmorphism variants for specific use cases
  glass: {
    subtle: withOpacity(designTokens.colors.white, 0.18), // Very light - Enhanced to 0.18 for more visibility
    light: withOpacity(designTokens.colors.white, 0.25), // Standard (same as card) - Enhanced to 0.25
    medium: withOpacity(designTokens.colors.white, 0.35), // Medium - Enhanced to 0.35
    strong: withOpacity(designTokens.colors.white, 0.45), // Strong - Enhanced to 0.45
    intense: withOpacity(designTokens.colors.white, 0.55), // Very strong - Enhanced to 0.55 for maximum white prominence
  },
};

// Centralized blur intensities for glassmorphism
export const blur = {
  light: 25,
  medium: 40,
  strong: 70,
  intense: 70,
  maximum: 90,
};
