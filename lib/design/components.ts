import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { designTokens, surfaces, withOpacity } from './system';

// Component style system - reusable component styles
export const componentStyles = StyleSheet.create({
  // CARD STYLES
  card: {
    backgroundColor: surfaces.card,
    borderWidth: 1,
    borderColor: surfaces.border,
    borderRadius: designTokens.radius.md,
    padding: designTokens.spacing.lg,
  } as ViewStyle,

  cardHover: {
    backgroundColor: surfaces.cardHover,
    borderColor: surfaces.borderHover,
  } as ViewStyle,

  cardActive: {
    backgroundColor: surfaces.cardActive,
    borderColor: surfaces.borderActive,
  } as ViewStyle,

  // Card variants
  cardSmall: {
    padding: designTokens.spacing.md,
  } as ViewStyle,

  cardLarge: {
    padding: designTokens.spacing.xl,
  } as ViewStyle,

  cardElevated: {
    ...designTokens.shadows.md,
  } as ViewStyle,

  // BUTTON STYLES
  button: {
    borderRadius: designTokens.radius.md,
    paddingHorizontal: designTokens.spacing.lg,
    paddingVertical: designTokens.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  } as ViewStyle,

  buttonSmall: {
    paddingHorizontal: designTokens.spacing.md,
    paddingVertical: designTokens.spacing.sm,
    borderRadius: designTokens.radius.sm,
  } as ViewStyle,

  buttonLarge: {
    paddingHorizontal: designTokens.spacing.xl,
    paddingVertical: designTokens.spacing.lg,
    borderRadius: designTokens.radius.lg,
  } as ViewStyle,

  // Button variants
  buttonPrimary: {
    backgroundColor: designTokens.colors.primary[600],
    borderWidth: 1,
    borderColor: designTokens.colors.primary[500],
  } as ViewStyle,

  buttonSecondary: {
    backgroundColor: surfaces.card,
    borderWidth: 1,
    borderColor: surfaces.border,
  } as ViewStyle,

  buttonGhost: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: 'transparent',
  } as ViewStyle,

  buttonSuccess: {
    backgroundColor: designTokens.colors.success[600],
    borderWidth: 1,
    borderColor: designTokens.colors.success[500],
  } as ViewStyle,

  buttonError: {
    backgroundColor: designTokens.colors.error[600],
    borderWidth: 1,
    borderColor: designTokens.colors.error[500],
  } as ViewStyle,

  buttonWarning: {
    backgroundColor: designTokens.colors.warning[600],
    borderWidth: 1,
    borderColor: designTokens.colors.warning[500],
  } as ViewStyle,

  buttonDisabled: {
    opacity: designTokens.opacity.disabled,
  } as ViewStyle,

  // CHIP/FILTER STYLES
  chip: {
    backgroundColor: surfaces.card,
    borderWidth: 1,
    borderColor: surfaces.border,
    borderRadius: designTokens.radius.md,
    paddingHorizontal: designTokens.spacing.lg,
    paddingVertical: designTokens.spacing.md,
  } as ViewStyle,

  chipSmall: {
    paddingHorizontal: designTokens.spacing.md,
    paddingVertical: designTokens.spacing.sm,
    borderRadius: designTokens.radius.sm,
  } as ViewStyle,

  chipActive: {
    backgroundColor: withOpacity(designTokens.colors.primary[500], 0.2),
    borderColor: withOpacity(designTokens.colors.primary[400], 0.4),
  } as ViewStyle,

  chipSecondaryActive: {
    backgroundColor: withOpacity(designTokens.colors.secondary[500], 0.2),
    borderColor: withOpacity(designTokens.colors.secondary[400], 0.4),
  } as ViewStyle,

  // BADGE STYLES
  badge: {
    borderRadius: designTokens.radius.sm,
    paddingHorizontal: designTokens.spacing.sm,
    paddingVertical: designTokens.spacing.xs,
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  badgeSuccess: {
    backgroundColor: withOpacity(designTokens.colors.success[500], 0.2),
    borderWidth: 1,
    borderColor: withOpacity(designTokens.colors.success[400], 0.4),
  } as ViewStyle,

  badgeError: {
    backgroundColor: withOpacity(designTokens.colors.error[500], 0.2),
    borderWidth: 1,
    borderColor: withOpacity(designTokens.colors.error[400], 0.4),
  } as ViewStyle,

  badgeWarning: {
    backgroundColor: withOpacity(designTokens.colors.warning[500], 0.2),
    borderWidth: 1,
    borderColor: withOpacity(designTokens.colors.warning[400], 0.4),
  } as ViewStyle,

  badgeInfo: {
    backgroundColor: withOpacity(designTokens.colors.info[500], 0.2),
    borderWidth: 1,
    borderColor: withOpacity(designTokens.colors.info[400], 0.4),
  } as ViewStyle,

  badgeLive: {
    backgroundColor: withOpacity(designTokens.colors.live[500], 0.2),
    borderWidth: 1,
    borderColor: withOpacity(designTokens.colors.live[400], 0.4),
  } as ViewStyle,

  badgeCategory: {
    backgroundColor: surfaces.card,
    borderWidth: 1,
    borderColor: surfaces.border,
  } as ViewStyle,

  // INPUT STYLES
  input: {
    backgroundColor: surfaces.card,
    borderWidth: 1,
    borderColor: surfaces.border,
    borderRadius: designTokens.radius.md,
    paddingHorizontal: designTokens.spacing.lg,
    paddingVertical: designTokens.spacing.md,
    fontSize: designTokens.typography.fontSize.base,
    color: surfaces.text.primary,
  } as ViewStyle & TextStyle,

  inputFocused: {
    borderColor: designTokens.colors.primary[500],
    backgroundColor: surfaces.cardHover,
  } as ViewStyle,

  inputError: {
    borderColor: designTokens.colors.error[500],
  } as ViewStyle,

  // CONTAINER STYLES
  container: {
    flex: 1,
    backgroundColor: surfaces.background,
  } as ViewStyle,

  containerPadded: {
    paddingHorizontal: designTokens.spacing.md,
  } as ViewStyle,

  containerSafe: {
    paddingTop: designTokens.spacing.md,
    paddingBottom: designTokens.spacing.md,
  } as ViewStyle,

  // LAYOUT STYLES
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  rowBetween: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  column: {
    flexDirection: 'column',
  } as ViewStyle,

  columnCenter: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  // SPACING UTILITIES
  gap_xs: { gap: designTokens.spacing.xs } as ViewStyle,
  gap_sm: { gap: designTokens.spacing.sm } as ViewStyle,
  gap_md: { gap: designTokens.spacing.md } as ViewStyle,
  gap_lg: { gap: designTokens.spacing.lg } as ViewStyle,
  gap_xl: { gap: designTokens.spacing.xl } as ViewStyle,

  // MARGIN UTILITIES
  m_xs: { margin: designTokens.spacing.xs } as ViewStyle,
  m_sm: { margin: designTokens.spacing.sm } as ViewStyle,
  m_md: { margin: designTokens.spacing.md } as ViewStyle,
  m_lg: { margin: designTokens.spacing.lg } as ViewStyle,
  m_xl: { margin: designTokens.spacing.xl } as ViewStyle,

  mt_xs: { marginTop: designTokens.spacing.xs } as ViewStyle,
  mt_sm: { marginTop: designTokens.spacing.sm } as ViewStyle,
  mt_md: { marginTop: designTokens.spacing.md } as ViewStyle,
  mt_lg: { marginTop: designTokens.spacing.lg } as ViewStyle,
  mt_xl: { marginTop: designTokens.spacing.xl } as ViewStyle,

  mb_xs: { marginBottom: designTokens.spacing.xs } as ViewStyle,
  mb_sm: { marginBottom: designTokens.spacing.sm } as ViewStyle,
  mb_md: { marginBottom: designTokens.spacing.md } as ViewStyle,
  mb_lg: { marginBottom: designTokens.spacing.lg } as ViewStyle,
  mb_xl: { marginBottom: designTokens.spacing.xl } as ViewStyle,

  ml_xs: { marginLeft: designTokens.spacing.xs } as ViewStyle,
  ml_sm: { marginLeft: designTokens.spacing.sm } as ViewStyle,
  ml_md: { marginLeft: designTokens.spacing.md } as ViewStyle,
  ml_lg: { marginLeft: designTokens.spacing.lg } as ViewStyle,
  ml_xl: { marginLeft: designTokens.spacing.xl } as ViewStyle,

  mr_xs: { marginRight: designTokens.spacing.xs } as ViewStyle,
  mr_sm: { marginRight: designTokens.spacing.sm } as ViewStyle,
  mr_md: { marginRight: designTokens.spacing.md } as ViewStyle,
  mr_lg: { marginRight: designTokens.spacing.lg } as ViewStyle,
  mr_xl: { marginRight: designTokens.spacing.xl } as ViewStyle,

  // PADDING UTILITIES
  p_xs: { padding: designTokens.spacing.xs } as ViewStyle,
  p_sm: { padding: designTokens.spacing.sm } as ViewStyle,
  p_md: { padding: designTokens.spacing.md } as ViewStyle,
  p_lg: { padding: designTokens.spacing.lg } as ViewStyle,
  p_xl: { padding: designTokens.spacing.xl } as ViewStyle,

  px_xs: { paddingHorizontal: designTokens.spacing.xs } as ViewStyle,
  px_sm: { paddingHorizontal: designTokens.spacing.sm } as ViewStyle,
  px_md: { paddingHorizontal: designTokens.spacing.md } as ViewStyle,
  px_lg: { paddingHorizontal: designTokens.spacing.lg } as ViewStyle,
  px_xl: { paddingHorizontal: designTokens.spacing.xl } as ViewStyle,

  py_xs: { paddingVertical: designTokens.spacing.xs } as ViewStyle,
  py_sm: { paddingVertical: designTokens.spacing.sm } as ViewStyle,
  py_md: { paddingVertical: designTokens.spacing.md } as ViewStyle,
  py_lg: { paddingVertical: designTokens.spacing.lg } as ViewStyle,
  py_xl: { paddingVertical: designTokens.spacing.xl } as ViewStyle,
});

// Typography styles
export const textStyles = StyleSheet.create({
  // HEADINGS
  h1: {
    fontSize: designTokens.typography.fontSize.xxxl,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    lineHeight: designTokens.typography.fontSize.xxxl * designTokens.typography.lineHeight.tight,
    letterSpacing: -0.5,
    color: surfaces.text.primary,
  } as TextStyle,

  h2: {
    fontSize: designTokens.typography.fontSize.xxl,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    lineHeight: designTokens.typography.fontSize.xxl * designTokens.typography.lineHeight.tight,
    letterSpacing: -0.5,
    color: surfaces.text.primary,
  } as TextStyle,

  h3: {
    fontSize: designTokens.typography.fontSize.xl,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    lineHeight: designTokens.typography.fontSize.xl * designTokens.typography.lineHeight.tight,
    letterSpacing: -0.5,
    color: surfaces.text.primary,
  } as TextStyle,

  h4: {
    fontSize: designTokens.typography.fontSize.lg,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    lineHeight: designTokens.typography.fontSize.lg * designTokens.typography.lineHeight.normal,
    letterSpacing: -0.5,
    color: surfaces.text.primary,
  } as TextStyle,

  // BODY TEXT
  body: {
    fontSize: designTokens.typography.fontSize.base,
    fontWeight: designTokens.typography.fontWeight.normal,
    lineHeight: designTokens.typography.fontSize.base * designTokens.typography.lineHeight.normal,
    color: surfaces.text.primary,
  } as TextStyle,

  bodySecondary: {
    fontSize: designTokens.typography.fontSize.base,
    fontWeight: designTokens.typography.fontWeight.normal,
    lineHeight: designTokens.typography.fontSize.base * designTokens.typography.lineHeight.normal,
    color: surfaces.text.secondary,
  } as TextStyle,

  bodySmall: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.normal,
    lineHeight: designTokens.typography.fontSize.sm * designTokens.typography.lineHeight.normal,
    color: surfaces.text.primary,
  } as TextStyle,

  bodySmallSecondary: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.normal,
    lineHeight: designTokens.typography.fontSize.sm * designTokens.typography.lineHeight.normal,
    color: surfaces.text.secondary,
  } as TextStyle,

  caption: {
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.normal,
    lineHeight: designTokens.typography.fontSize.xs * designTokens.typography.lineHeight.normal,
    color: surfaces.text.tertiary,
  } as TextStyle,

  // BUTTON TEXT
  buttonText: {
    fontSize: designTokens.typography.fontSize.base,
    fontWeight: designTokens.typography.fontWeight.semibold,
    color: surfaces.text.primary,
  } as TextStyle,

  buttonTextSmall: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
    color: surfaces.text.primary,
  } as TextStyle,

  buttonTextLarge: {
    fontSize: designTokens.typography.fontSize.lg,
    fontWeight: designTokens.typography.fontWeight.semibold,
    color: surfaces.text.primary,
  } as TextStyle,

  // BADGE TEXT
  badgeText: {
    fontSize: designTokens.typography.fontSize.xs,
    fontWeight: designTokens.typography.fontWeight.normal,
    textTransform: 'uppercase',
    padding: 3,
    letterSpacing: 0.5,
    color: surfaces.text.primary,
  } as TextStyle,

  // CHIP TEXT
  chipText: {
    fontSize: designTokens.typography.fontSize.sm,
    fontWeight: designTokens.typography.fontWeight.semibold,
    color: surfaces.text.secondary,
  } as TextStyle,

  chipTextActive: {
    color: designTokens.colors.primary[400],
  } as TextStyle,

  chipTextSecondaryActive: {
    color: designTokens.colors.secondary[400],
  } as TextStyle,

  // UTILITY TEXT STYLES
  textCenter: { textAlign: 'center' } as TextStyle,
  textLeft: { textAlign: 'left' } as TextStyle,
  textRight: { textAlign: 'right' } as TextStyle,

  textPrimary: { color: surfaces.text.primary } as TextStyle,
  textSecondary: { color: surfaces.text.secondary } as TextStyle,
  textTertiary: { color: surfaces.text.tertiary } as TextStyle,
  textDisabled: { color: surfaces.text.disabled } as TextStyle,

  textSuccess: { color: designTokens.colors.success[400] } as TextStyle,
  textError: { color: designTokens.colors.error[400] } as TextStyle,
  textWarning: { color: designTokens.colors.warning[400] } as TextStyle,
  textInfo: { color: designTokens.colors.info[400] } as TextStyle,
  textLive: { color: designTokens.colors.live[400] } as TextStyle,
});
