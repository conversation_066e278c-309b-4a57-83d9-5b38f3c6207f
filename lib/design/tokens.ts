// Design system tokens entrypoint (as documented in docs/design-system.md)
// Provides a stable tokens API built on top of the core system exports

import { designTokens, surfaces, withOpacity } from './system';
import { textStyles } from './components';
import type { TextStyle, ViewStyle } from 'react-native';

// Spacing: extend with 2xl..6xl friendly keys used in docs/components
const spacing = {
  ...designTokens.spacing,
  // Aliases following the docs scale (8px base)
  '2xl': 40,
  '3xl': 48,
  '4xl': 64,
  '5xl': 80,
  '6xl': 96,
} as const;

// Radius - directly from system
const radius = {
  ...designTokens.radius,
} as const;

// Colors - expose base palettes plus semantic surface/text groupings used in code
const colors = {
  ...designTokens.colors,
  text: {
    primary: surfaces.text.primary,
    secondary: surfaces.text.secondary,
    tertiary: surfaces.text.tertiary,
    quaternary: surfaces.text.disabled,
  },
  cardText: {
    primary: surfaces.cardText.primary,
    secondary: surfaces.cardText.secondary,
    tertiary: surfaces.cardText.tertiary,
    disabled: surfaces.cardText.disabled,
  },
  surface: {
    glassLight: surfaces.card,
    glassMedium: surfaces.cardHover,
    glassStrong: surfaces.cardActive,
    glassBorder: surfaces.border,
  },
  // Legacy alias used in screens: map 'prayer' to secondary palette
  prayer: designTokens.colors.secondary,
} as const;

// Blur intensities (docs)
const blur = {
  light: 25,
  medium: 40,
  strong: 55,
  intense: 70,
  maximum: 90,
} as const;

// Typography mapping to documented scale using textStyles
const typography = {
  display: {
    large: textStyles.h1 as TextStyle,
    medium: textStyles.h2 as TextStyle,
  },
  heading: {
    h1: textStyles.h1 as TextStyle,
    h2: textStyles.h2 as TextStyle,
    h3: textStyles.h3 as TextStyle,
    h4: textStyles.h4 as TextStyle,
  },
  body: {
    large: textStyles.body as TextStyle,
    medium: textStyles.body as TextStyle,
    small: textStyles.bodySmall as TextStyle,
  },
  label: {
    large: textStyles.buttonText as TextStyle,
    medium: textStyles.buttonTextSmall as TextStyle,
    small: textStyles.caption as TextStyle,
  },
  caption: {
    medium: textStyles.caption as TextStyle,
  },
} as const;

// Minimal semantic colors used in current screens
const semanticColors = {
  status: {
    live: {
      dot: colors.live[400],
      text: colors.live[400],
    },
    answered: {
      text: colors.success[400],
    },
  },
} as const;

// Additional layout utilities referenced in screens
const layout = {
  header: {
    paddingTop: spacing.xl,
  },
} as const;

const lineHeight = {
  lg: 24,
} as const;

// withOpacity passthrough
const tokens = {
  spacing,
  radius,
  colors,
  typography,
  blur,
  withOpacity,
  semanticColors,
  layout,
  lineHeight,
};

export { tokens, spacing, radius, colors, typography, blur };
export type { TextStyle, ViewStyle };
