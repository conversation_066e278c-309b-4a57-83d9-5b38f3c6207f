// Main design system export
// Import locally so we can safely reference designTokens in this module
import { designTokens, surfaces, withOpacity, blur } from './system';
import { componentStyles, textStyles } from './components';
import { tokens as dsTokens } from './tokens';

export { designTokens, surfaces, withOpacity, blur };
export { componentStyles, textStyles };

// Re-export for convenience
export const { spacing, radius, typography, colors, shadows, opacity } = designTokens;

// Note: blur is now imported from system.ts for centralization

// Commonly used combinations
export const commonStyles = {
  // Screen containers
  screen: [componentStyles.container, componentStyles.containerPadded],
  screenSafe: [
    componentStyles.container,
    componentStyles.containerPadded,
    componentStyles.containerSafe,
  ],

  // Card variations
  prayerCard: [componentStyles.card, componentStyles.cardElevated],
  filterCard: [componentStyles.card, componentStyles.cardSmall],

  // Button variations
  primaryButton: [componentStyles.button, componentStyles.buttonPrimary],
  secondaryButton: [componentStyles.button, componentStyles.buttonSecondary],
  ghostButton: [componentStyles.button, componentStyles.buttonGhost],

  // Common layouts
  headerRow: [componentStyles.rowBetween, componentStyles.mb_md],
  contentRow: [componentStyles.row, componentStyles.gap_md],
  badgeRow: [componentStyles.row, componentStyles.gap_sm],

  // Standardized app typography (Create Prayer standard)
  screenTitle: {
    fontSize: 30,
    fontWeight: '300',
    fontFamily: 'Roboto-Light',
    letterSpacing: -0.5,
    color: 'white',
    textAlign: 'center' as const,
  },
  screenSubtitle: {
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    color: withOpacity(surfaces.text.primary, 0.7),
    lineHeight: 24,
    textAlign: 'center' as const,
  },

  // Text combinations
  cardTitle: [textStyles.h4, componentStyles.mb_sm],
  cardBody: [textStyles.bodySecondary, componentStyles.mb_md],
  cardMeta: [textStyles.caption, componentStyles.mt_sm],
};

// Helper hooks for consistent styling
import { useMemo } from 'react';

export const useDesignSystem = () => {
  return useMemo(
    () => ({
      tokens: designTokens,
      surfaces,
      components: componentStyles,
      text: textStyles,
      common: commonStyles,
      withOpacity,
      tokensCompat: dsTokens,
    }),
    [],
  );
};

// Theme-aware color helpers
export const getStatusColor = (status: 'success' | 'error' | 'warning' | 'info' | 'live') => {
  switch (status) {
    case 'success':
      return colors.success[400];
    case 'error':
      return colors.error[400];
    case 'warning':
      return colors.warning[400];
    case 'info':
      return colors.info[400];
    case 'live':
      return colors.live[400];
    default:
      return surfaces.text.secondary;
  }
};

export const getStatusBadgeStyle = (
  status: 'success' | 'error' | 'warning' | 'info' | 'live' | 'category',
) => {
  switch (status) {
    case 'success':
      return [componentStyles.badge, componentStyles.badgeSuccess];
    case 'error':
      return [componentStyles.badge, componentStyles.badgeError];
    case 'warning':
      return [componentStyles.badge, componentStyles.badgeWarning];
    case 'info':
      return [componentStyles.badge, componentStyles.badgeInfo];
    case 'live':
      return [componentStyles.badge, componentStyles.badgeLive];
    case 'category':
      return [componentStyles.badge, componentStyles.badgeCategory];
    default:
      return [componentStyles.badge];
  }
};

// Note: Removed getResponsiveSpacing and getResponsiveFontSize - unused and premature for current needs

// Legacy compatibility - keep the old useDesignTokens hook working
export const useDesignTokens = () => {
  return {
    ...designTokens,
    colors: {
      ...designTokens.colors,
      text: {
        primary: surfaces.text.primary,
        secondary: surfaces.text.secondary,
        tertiary: surfaces.text.tertiary,
        disabled: surfaces.text.disabled,
      },
      surface: {
        glassLight: surfaces.card,
        glassMedium: surfaces.cardHover,
        glassStrong: surfaces.cardActive,
        glassBorder: surfaces.border,
      },
      prayer: designTokens.colors.secondary,
    },
    blur,
    withOpacity,
  };
};

// Enhanced compatibility for old components
export const createStyles = (styleFactory: (tokens: any) => any) => {
  const tokens = {
    ...designTokens,
    // Provide spacing aliases used in legacy styles (e.g., '2xl')
    spacing: {
      ...designTokens.spacing,
      '2xl': 40,
      '3xl': 48,
      '4xl': 64,
      '5xl': 80,
      '6xl': 96,
    },
    withOpacity,
    colors: {
      ...designTokens.colors,
      text: {
        primary: surfaces.text.primary,
        secondary: surfaces.text.secondary,
        tertiary: surfaces.text.tertiary,
        disabled: surfaces.text.disabled,
        // Case-insensitive aliases for legacy code paths
        Primary: surfaces.text.primary,
        Secondary: surfaces.text.secondary,
        Tertiary: surfaces.text.tertiary,
        Disabled: surfaces.text.disabled,
      },
      background: {
        primary: surfaces.background,
      },
      surface: {
        glassLight: surfaces.card,
        glassMedium: surfaces.cardHover,
        glassStrong: surfaces.cardActive,
        glassBorder: surfaces.border,
      },
      // Legacy alias used in some screens
      prayer: designTokens.colors.secondary,
    },
    // Typography mapping to new textStyles for legacy usage
    typography: {
      display: {
        large: textStyles.h1,
        medium: textStyles.h2,
      },
      heading: {
        h1: textStyles.h1,
        h2: textStyles.h2,
        h3: textStyles.h3,
        h4: textStyles.h4,
      },
      body: {
        large: textStyles.body,
        medium: textStyles.body,
        small: textStyles.bodySmall,
      },
      label: {
        large: textStyles.buttonText,
        medium: textStyles.buttonTextSmall,
        small: textStyles.caption,
      },
      button: {
        small: textStyles.buttonTextSmall,
        medium: textStyles.buttonText,
        large: textStyles.buttonTextLarge,
      },
      caption: {
        medium: textStyles.caption,
      },
    },
    blur,
    layout: {
      header: {
        paddingTop: spacing.xl,
      },
    },
    lineHeight: {
      lg: 24,
    },
    semanticColors: {
      status: {
        live: {
          dot: designTokens.colors.live[400],
          text: designTokens.colors.live[400],
        },
        answered: {
          text: designTokens.colors.success[400],
        },
      },
    },
  };
  return styleFactory(tokens);
};

export const mixins = {
  buttonBase: (size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizes = {
      sm: {
        paddingHorizontal: spacing.md,
        paddingVertical: spacing.sm,
        minHeight: 36,
      },
      md: {
        paddingHorizontal: spacing.lg,
        paddingVertical: spacing.md,
        minHeight: 44,
      },
      lg: {
        paddingHorizontal: spacing.xl,
        paddingVertical: spacing.lg,
        minHeight: 52,
      },
    };

    return {
      ...sizes[size],
      borderRadius: designTokens.radius.sm, // 8px throughout
      borderWidth: 1,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      flexDirection: 'row' as const,
    };
  },
  glassCard: (variant: 'subtle' | 'light' | 'medium' | 'strong' | 'intense' = 'light') => {
    const variants = {
      subtle: {
        backgroundColor: surfaces.glass.subtle,
        borderColor: surfaces.border,
      },
      light: {
        backgroundColor: surfaces.glass.light,
        borderColor: surfaces.border,
      },
      medium: {
        backgroundColor: surfaces.glass.medium,
        borderColor: surfaces.border,
      },
      strong: {
        backgroundColor: surfaces.glass.strong,
        borderColor: surfaces.border,
      },
      intense: {
        backgroundColor: surfaces.glass.intense,
        borderColor: surfaces.borderActive,
      },
    };

    return {
      ...variants[variant],
      borderWidth: 1,
      borderRadius: designTokens.radius.sm, // 8px throughout
      // Note: For React Native, BlurView component should wrap this style for true glassmorphism
    };
  },
  layout: {
    screenPadding: {
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
    },
  },
};

export const variants = {
  button: {
    primary: {
      backgroundColor: colors.primary[400],
      borderColor: colors.primary[500],
    },
    secondary: {
      backgroundColor: surfaces.glass.light,
      borderColor: surfaces.border,
    },
    destructive: {
      backgroundColor: withOpacity(colors.error[400], 0.8),
      borderColor: colors.error[400],
    },
    prayer: {
      backgroundColor: withOpacity(colors.secondary[400], 0.8),
      borderColor: colors.secondary[400],
    },
  },
  chip: {
    default: {
      backgroundColor: surfaces.glass.light,
      borderColor: surfaces.border,
    },
    selected: {
      backgroundColor: withOpacity(colors.primary[400], 0.8),
      borderColor: colors.primary[400],
    },
    category: {
      backgroundColor: surfaces.glass.subtle,
      borderColor: surfaces.border,
    },
  },
  status: {
    live: {
      backgroundColor: withOpacity(colors.live[500], 0.25),
      borderWidth: 1,
      borderColor: withOpacity(colors.live[400], 0.4),
    },
    answered: {
      backgroundColor: withOpacity(colors.success[500], 0.25),
      borderWidth: 1,
      borderColor: withOpacity(colors.success[400], 0.4),
    },
  },
};
