import { Room, RoomOptions } from 'livekit-client';

// LiveKit configuration
export const LIVEKIT_CONFIG = {
  // WebSocket URL from environment variables
  wsURL: process.env.EXPO_PUBLIC_LIVEKIT_URL || 'wss://your-livekit-server.com',
  // Note: API Key and Secret are now handled securely via Supabase Edge Function
  // They should not be exposed in the client-side code
};

export interface BroadcastRoom {
  id: string;
  name: string;
  prayerId: string;
  hostUserId: string;
  isActive: boolean;
  participantCount: number;
  createdAt: Date;
}

export interface BroadcastParticipant {
  id: string;
  name: string;
  isMuted: boolean;
  isHost: boolean;
  joinedAt: Date;
}

// Room configuration for prayer broadcasts - React Native optimized
export const getRoomOptions = (): RoomOptions => ({
  // Audio-only broadcasts for prayer sessions
  publishDefaults: {
    // audio-only defaults (no video)
    // leave video-related defaults undefined for RN client
  },
  // React Native specific settings
  adaptiveStream: {
    pixelDensity: 'screen',
  },
  // Keep connection alive
  disconnectOnPageLeave: false, // Important for React Native

  // Let LiveKit handle audio management properly
});

// Generate room name for prayer broadcasts
export const generateRoomName = (prayerId: string): string => {
  return `prayer-broadcast-${prayerId}`;
};

// Generate LiveKit access token via Supabase Edge Function
export const generateAccessToken = async (
  roomName: string,
  participantName: string,
  isHost: boolean = false,
): Promise<string> => {
  // Clean room name - be extra strict
  const safeRoomName =
    String(roomName || '')
      .trim()
      .replace(/[^a-zA-Z0-9\-_]/g, '')
      .toLowerCase() || // Ensure lowercase
    'default-room';

  // LiveKit requires strict alphanumeric names
  const safeName =
    String(participantName || '')
      .trim()
      .replace(/[^a-zA-Z0-9]/g, '') // Only alphanumeric characters
      .toLowerCase() // LiveKit expects lowercase
      .substring(0, 16) || // Reasonable length limit
    'user' + Math.random().toString(36).substr(2, 5); // Fallback with random suffix

  console.log(
    `🔄 Generating token for: "${participantName}" -> "${safeName}" in room: "${roomName}" -> "${safeRoomName}" (host: ${isHost})`,
  );

  // Basic validation
  if (!safeRoomName || safeRoomName.length < 3) {
    throw new Error('Invalid room name provided');
  }

  if (!safeName || safeName.length < 1) {
    throw new Error('Invalid participant name provided');
  }

  try {
    // Import supabase here to avoid circular dependencies
    const { supabase } = await import('./supabase');

    // Get the current session to get the auth token
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('❌ Authentication failed:', sessionError || 'No session');
      throw new Error('User must be authenticated to generate LiveKit token');
    }

    // Call the Supabase Edge Function to generate the token
    const { data, error } = await supabase.functions.invoke('livekit-token', {
      body: {
        roomName: safeRoomName,
        participantName: safeName,
        isHost: Boolean(isHost),
      },
      headers: {
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (error) {
      console.error('Error calling livekit-token function:', error);
      throw new Error(`Failed to generate token: ${error.message}`);
    }

    if (!data?.token) {
      console.error('No token returned from function:', data);
      throw new Error('No token returned from server');
    }

    console.log('✅ Generated LiveKit token successfully');
    return data.token;
  } catch (error) {
    console.error('❌ Failed to generate LiveKit token:', error);
    throw new Error(
      `Token generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

// Room management utilities
export class BroadcastManager {
  private static instance: BroadcastManager;
  private activeRooms: Map<string, Room> = new Map();

  static getInstance(): BroadcastManager {
    if (!BroadcastManager.instance) {
      BroadcastManager.instance = new BroadcastManager();
    }
    return BroadcastManager.instance;
  }

  async createRoom(prayerId: string, hostId: string): Promise<Room> {
    const roomName = generateRoomName(prayerId);

    // Check if room already exists and is connected
    const existingRoom = this.activeRooms.get(roomName);
    if (existingRoom && existingRoom.state === 'connected') {
      console.log(`🔄 Returning existing connected room: ${roomName}`);
      return existingRoom;
    }

    // Clean up any disconnected room
    if (existingRoom) {
      try {
        await existingRoom.disconnect();
      } catch (e) {
        console.warn('Error disconnecting old room:', e);
      }
      this.activeRooms.delete(roomName);
    }

    console.log(`🏗️ Creating room: ${roomName} for host: ${hostId}`);

    const room = new Room(getRoomOptions());

    try {
      const token = await generateAccessToken(roomName, hostId, true);

      console.log(`🔗 Connecting to LiveKit: ${LIVEKIT_CONFIG.wsURL}`);

      // Add timeout for connection
      const connectPromise = room.connect(LIVEKIT_CONFIG.wsURL, token);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout after 20 seconds')), 20000),
      );

      await Promise.race([connectPromise, timeoutPromise]);

      this.activeRooms.set(roomName, room);
      console.log(`✅ Room created successfully: ${roomName}`);

      return room;
    } catch (error) {
      console.error(`❌ Failed to create room ${roomName}:`, error);

      // Clean up room on failure
      try {
        await room.disconnect();
      } catch (cleanupError) {
        console.error('Error cleaning up failed room:', cleanupError);
      }

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('toLowerCase')) {
          throw new Error(
            'LiveKit connection failed due to participant name issue. Please try again.',
          );
        } else if (error.message.includes('timeout')) {
          throw new Error(
            'Connection timed out. Please check your internet connection and try again.',
          );
        } else if (error.message.includes('token')) {
          throw new Error('Authentication failed. Please try again.');
        }
      }

      throw error;
    }
  }

  async joinRoom(prayerId: string, participantId: string): Promise<Room> {
    const roomName = generateRoomName(prayerId);

    console.log(`🚪 Joining room: ${roomName} as: ${participantId}`);

    const room = new Room(getRoomOptions());

    try {
      const token = await generateAccessToken(roomName, participantId, false);

      console.log(`🔗 Connecting to LiveKit: ${LIVEKIT_CONFIG.wsURL}`);
      await room.connect(LIVEKIT_CONFIG.wsURL, token);

      console.log(`✅ Joined room successfully: ${roomName}`);
      return room;
    } catch (error) {
      console.error(`❌ Failed to join room ${roomName}:`, error);
      throw error;
    }
  }

  async leaveRoom(prayerId: string): Promise<void> {
    const roomName = generateRoomName(prayerId);
    const room = this.activeRooms.get(roomName);

    if (room) {
      await room.disconnect();
      this.activeRooms.delete(roomName);
    }
  }

  getActiveRoom(prayerId: string): Room | undefined {
    const roomName = generateRoomName(prayerId);
    return this.activeRooms.get(roomName);
  }

  async cleanup(): Promise<void> {
    const disconnectPromises = Array.from(this.activeRooms.values()).map((room) =>
      room.disconnect(),
    );
    await Promise.all(disconnectPromises);
    this.activeRooms.clear();
  }
}
