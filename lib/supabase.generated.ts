export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '13.0.4';
  };
  public: {
    Tables: {
      comment_likes: {
        Row: {
          comment_id: string;
          created_at: string | null;
          id: string;
          user_id: string;
        };
        Insert: {
          comment_id: string;
          created_at?: string | null;
          id?: string;
          user_id: string;
        };
        Update: {
          comment_id?: string;
          created_at?: string | null;
          id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'comment_likes_comment_id_fkey';
            columns: ['comment_id'];
            isOneToOne: false;
            referencedRelation: 'prayer_comments';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'comment_likes_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      comment_reports: {
        Row: {
          comment_id: string;
          created_at: string | null;
          description: string | null;
          id: string;
          reason: string;
          reporter_id: string;
          status: string | null;
          updated_at: string | null;
        };
        Insert: {
          comment_id: string;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          reason: string;
          reporter_id: string;
          status?: string | null;
          updated_at?: string | null;
        };
        Update: {
          comment_id?: string;
          created_at?: string | null;
          description?: string | null;
          id?: string;
          reason?: string;
          reporter_id?: string;
          status?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'comment_reports_comment_id_fkey';
            columns: ['comment_id'];
            isOneToOne: false;
            referencedRelation: 'prayer_comments';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'comment_reports_reporter_id_fkey';
            columns: ['reporter_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      device_tokens: {
        Row: {
          created_at: string;
          id: string;
          notifications_enabled: boolean;
          platform: string;
          token: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          notifications_enabled?: boolean;
          platform: string;
          token: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          notifications_enabled?: boolean;
          platform?: string;
          token?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      prayer_comments: {
        Row: {
          content: string;
          created_at: string | null;
          id: string;
          is_reported: boolean | null;
          like_count: number | null;
          parent_id: string | null;
          prayer_id: string;
          reply_count: number | null;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          content: string;
          created_at?: string | null;
          id?: string;
          is_reported?: boolean | null;
          like_count?: number | null;
          parent_id?: string | null;
          prayer_id: string;
          reply_count?: number | null;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          content?: string;
          created_at?: string | null;
          id?: string;
          is_reported?: boolean | null;
          like_count?: number | null;
          parent_id?: string | null;
          prayer_id?: string;
          reply_count?: number | null;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prayer_comments_parent_id_fkey';
            columns: ['parent_id'];
            isOneToOne: false;
            referencedRelation: 'prayer_comments';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'prayer_comments_prayer_id_fkey';
            columns: ['prayer_id'];
            isOneToOne: false;
            referencedRelation: 'prayers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'prayer_comments_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      prayer_joins: {
        Row: {
          created_at: string | null;
          id: string;
          prayer_id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          prayer_id: string;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          prayer_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prayer_joins_prayer_id_fkey';
            columns: ['prayer_id'];
            isOneToOne: false;
            referencedRelation: 'prayers';
            referencedColumns: ['id'];
          },
        ];
      };
      prayer_likes: {
        Row: {
          created_at: string;
          id: string;
          prayer_id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          prayer_id: string;
          user_id: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          prayer_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prayer_likes_prayer_id_fkey';
            columns: ['prayer_id'];
            isOneToOne: false;
            referencedRelation: 'prayers';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'prayer_likes_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      prayer_outcomes: {
        Row: {
          created_at: string;
          id: string;
          outcome_text: string;
          outcome_type: string | null;
          prayer_id: string;
          updated_at: string;
        };
        Insert: {
          created_at?: string;
          id?: string;
          outcome_text: string;
          outcome_type?: string | null;
          prayer_id: string;
          updated_at?: string;
        };
        Update: {
          created_at?: string;
          id?: string;
          outcome_text?: string;
          outcome_type?: string | null;
          prayer_id?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prayer_outcomes_prayer_id_fkey';
            columns: ['prayer_id'];
            isOneToOne: true;
            referencedRelation: 'prayers';
            referencedColumns: ['id'];
          },
        ];
      };
      prayers: {
        Row: {
          allows_broadcast: boolean | null;
          category: string;
          comment_count: number | null;
          created_at: string | null;
          description: string;
          duration_minutes: number | null;
          id: string;
          is_anonymous: boolean | null;
          is_answered: boolean | null;
          is_live: boolean | null;
          like_count: number | null;
          prayer_text: string | null;
          scheduled_at: string | null;
          title: string;
          updated_at: string | null;
          user_id: string;
        };
        Insert: {
          allows_broadcast?: boolean | null;
          category?: string;
          comment_count?: number | null;
          created_at?: string | null;
          description: string;
          duration_minutes?: number | null;
          id?: string;
          is_anonymous?: boolean | null;
          is_answered?: boolean | null;
          is_live?: boolean | null;
          like_count?: number | null;
          prayer_text?: string | null;
          scheduled_at?: string | null;
          title: string;
          updated_at?: string | null;
          user_id: string;
        };
        Update: {
          allows_broadcast?: boolean | null;
          category?: string;
          comment_count?: number | null;
          created_at?: string | null;
          description?: string;
          duration_minutes?: number | null;
          id?: string;
          is_anonymous?: boolean | null;
          is_answered?: boolean | null;
          is_live?: boolean | null;
          like_count?: number | null;
          prayer_text?: string | null;
          scheduled_at?: string | null;
          title?: string;
          updated_at?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'prayers_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      profile_likes: {
        Row: {
          created_at: string | null;
          id: string;
          profile_id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          profile_id: string;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          profile_id?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'profile_likes_profile_id_fkey';
            columns: ['profile_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'profile_likes_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      user_follows: {
        Row: {
          created_at: string | null;
          follower_id: string;
          following_id: string;
          id: string;
        };
        Insert: {
          created_at?: string | null;
          follower_id: string;
          following_id: string;
          id?: string;
        };
        Update: {
          created_at?: string | null;
          follower_id?: string;
          following_id?: string;
          id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'user_follows_follower_id_fkey';
            columns: ['follower_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'user_follows_following_id_fkey';
            columns: ['following_id'];
            isOneToOne: false;
            referencedRelation: 'user_profiles';
            referencedColumns: ['id'];
          },
        ];
      };
      user_profiles: {
        Row: {
          avatar_url: string | null;
          bio: string | null;
          comments_made: number | null;
          created_at: string | null;
          days_active: number | null;
          display_name: string | null;
          email: string | null;
          follower_count: number | null;
          following_count: number | null;
          full_name: string | null;
          id: string;
          is_premium: boolean | null;
          location: string | null;
          prayer_count: number | null;
          prayer_time: number | null;
          updated_at: string | null;
          username: string | null;
          website: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          bio?: string | null;
          comments_made?: number | null;
          created_at?: string | null;
          days_active?: number | null;
          display_name?: string | null;
          email?: string | null;
          follower_count?: number | null;
          following_count?: number | null;
          full_name?: string | null;
          id: string;
          is_premium?: boolean | null;
          location?: string | null;
          prayer_count?: number | null;
          prayer_time?: number | null;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          bio?: string | null;
          comments_made?: number | null;
          created_at?: string | null;
          days_active?: number | null;
          display_name?: string | null;
          email?: string | null;
          follower_count?: number | null;
          following_count?: number | null;
          full_name?: string | null;
          id?: string;
          is_premium?: boolean | null;
          location?: string | null;
          prayer_count?: number | null;
          prayer_time?: number | null;
          updated_at?: string | null;
          username?: string | null;
          website?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>;

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] & DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals;
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals;
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
