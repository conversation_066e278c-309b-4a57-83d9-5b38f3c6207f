/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './App.{js,jsx,ts,tsx}',
    './app/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
  ],
  theme: {
    extend: {
      // 8px-based spacing system
      spacing: {
        xs: '4px', // 0.5x
        sm: '8px', // 1x
        md: '16px', // 2x
        lg: '24px', // 3x
        xl: '32px', // 4x
        '2xl': '40px', // 5x
        '3xl': '48px', // 6x
        '4xl': '64px', // 8x
        '5xl': '80px', // 10x
        '6xl': '96px', // 12x
      },

      // Consistent border radius (small throughout)
      borderRadius: {
        xs: '4px',
        sm: '8px', // Primary radius
        md: '8px', // Keep consistent
        lg: '8px', // Keep consistent
        xl: '12px', // Slightly larger for special cases
        '2xl': '16px', // For large components
        full: '9999px',
      },

      // Prayer app color palette
      colors: {
        // Primary colors (Purple/Blue theme)
        primary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#8b5cf6', // Main purple
          600: '#7c3aed',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
        },

        // Success colors (Green)
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e', // Main green
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },

        // Error colors (Red)
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444', // Main red
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },

        // Warning colors (Orange)
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b', // Main orange
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },

        // Info colors (Blue)
        info: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6', // Main blue
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        },

        // Prayer-specific colors
        prayer: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899', // Main prayer pink
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        },

        // Surface colors for glassmorphism
        surface: {
          'glass-light': 'rgba(255, 255, 255, 0.12)', // Enhanced from 0.05 to 0.12
          'glass-medium': 'rgba(255, 255, 255, 0.25)', // Enhanced from 0.1 to 0.25
          'glass-strong': 'rgba(255, 255, 255, 0.32)', // Enhanced from 0.15 to 0.32
          'glass-border': 'rgba(255, 255, 255, 0.4)', // Enhanced from 0.2 to 0.4
        },

        // Text colors
        text: {
          primary: '#ffffff',
          secondary: 'rgba(255, 255, 255, 0.8)',
          tertiary: 'rgba(255, 255, 255, 0.6)',
          quaternary: 'rgba(255, 255, 255, 0.4)',
          disabled: 'rgba(255, 255, 255, 0.3)',
        },
      },

      // Typography scale
      fontSize: {
        xs: ['12px', { lineHeight: '16px' }],
        sm: ['14px', { lineHeight: '20px' }],
        base: ['16px', { lineHeight: '24px' }],
        lg: ['18px', { lineHeight: '28px' }],
        xl: ['20px', { lineHeight: '28px' }],
        '2xl': ['24px', { lineHeight: '32px' }],
        '3xl': ['30px', { lineHeight: '36px' }],
        '4xl': ['36px', { lineHeight: '40px' }],
      },

      // Box shadows for depth
      boxShadow: {
        glass: '0 8px 32px rgba(0, 0, 0, 0.1)',
        'glass-lg': '0 12px 40px rgba(0, 0, 0, 0.15)',
        prayer: '0 8px 32px rgba(139, 92, 246, 0.2)',
        success: '0 8px 32px rgba(34, 197, 94, 0.2)',
        error: '0 8px 32px rgba(239, 68, 68, 0.2)',
      },

      // Animation durations
      transitionDuration: {
        250: '250ms',
        350: '350ms',
        400: '400ms',
        600: '600ms',
      },
    },
  },
  plugins: [],
};
