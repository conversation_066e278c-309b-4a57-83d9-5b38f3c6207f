# LiveKit Configuration for The Power of Many App
# Copy this file to .env and fill in your actual values

# Your LiveKit WebSocket URL (get this from LiveKit Cloud dashboard)
# This is safe to include in client-side code
EXPO_PUBLIC_LIVEKIT_URL=wss://your-project.livekit.cloud

# SECURITY NOTE: 
# DO NOT put API_KEY and API_SECRET in this .env file!
# Those credentials should ONLY be stored as Supabase secrets for security:
#
# npx supabase secrets set LIVEKIT_API_KEY=your-api-key --project-ref YOUR_PROJECT_REF
# npx supabase secrets set LIVEKIT_API_SECRET=your-api-secret --project-ref YOUR_PROJECT_REF
# npx supabase secrets set LIVEKIT_URL=wss://your-project.livekit.cloud --project-ref YOUR_PROJECT_REF
