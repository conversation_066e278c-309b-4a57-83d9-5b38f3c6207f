import { useState } from 'react';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from './useToast';

export function useProfileImage() {
  const { user, updateProfile } = useAuth();
  const toast = useToast();
  const [uploading, setUploading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      toast.showErrorToast(
        'Permission needed',
        'Camera roll permissions are required to upload photos',
      );
      return false;
    }
    return true;
  };

  const compressImage = async (uri: string) => {
    try {
      // First, resize to a reasonable size while maintaining aspect ratio
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          { resize: { width: 512 } }, // Resize to max width 512px, height auto
        ],
        {
          compress: 0.85, // 85% quality for better balance
          format: ImageManipulator.SaveFormat.JPEG,
        },
      );

      console.log('Image compressed from', uri, 'to', manipulatedImage.uri);
      console.log('Image dimensions:', manipulatedImage.width, 'x', manipulatedImage.height);

      return manipulatedImage.uri;
    } catch (error) {
      console.error('Error compressing image:', error);
      return uri; // Return original if compression fails
    }
  };

  const uploadImage = async (uri: string): Promise<string | null> => {
    if (!user) {
      toast.showErrorToast('Error', 'User not authenticated');
      return null;
    }

    try {
      // Compress the image first
      const compressedUri = await compressImage(uri);

      // Use the official Supabase recommended method for React Native
      console.log('🖼️ Using OFFICIAL Supabase React Native method with base64-arraybuffer');

      // Read file as base64 using FileSystem (as recommended by Supabase)
      const base64 = await FileSystem.readAsStringAsync(compressedUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Use decode from base64-arraybuffer (the official Supabase way)
      const arrayBuffer = decode(base64);

      console.log('Created ArrayBuffer from base64, size:', arrayBuffer.byteLength, 'bytes');

      // Clean up old profile images first
      try {
        const { data: existingFiles } = await supabase.storage.from('profile-images').list(user.id);

        if (existingFiles && existingFiles.length > 0) {
          const filesToDelete = existingFiles.map((file) => `${user.id}/${file.name}`);
          await supabase.storage.from('profile-images').remove(filesToDelete);
          console.log('Cleaned up old profile images:', filesToDelete);
        }
      } catch (cleanupError) {
        console.log('Note: Could not clean up old images:', cleanupError);
        // Don't fail the upload if cleanup fails
      }

      // Create unique filename with timestamp to avoid caching issues
      const timestamp = Date.now();
      const fileName = `${user.id}/profile_${timestamp}.jpg`;

      // Upload to Supabase storage using ArrayBuffer (official method)
      const { data, error } = await supabase.storage
        .from('profile-images')
        .upload(fileName, arrayBuffer, {
          contentType: 'image/jpeg',
          cacheControl: '3600', // Cache for 1 hour
        });

      if (error) {
        console.error('Upload error:', error);
        toast.showErrorToast('Upload failed', error.message);
        return null;
      }

      console.log('Upload successful:', data);

      // Get public URL
      const { data: urlData } = supabase.storage.from('profile-images').getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.showErrorToast('Upload failed', 'Something went wrong');
      return null;
    }
  };

  const pickAndUploadImage = async () => {
    // Request permissions first
    const hasPermission = await requestPermissions();
    if (!hasPermission) return null;

    setUploading(true);

    try {
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 1.0, // Start with highest quality, we'll compress later
        allowsMultipleSelection: false,
      });

      if (result.canceled) {
        setUploading(false);
        return null;
      }

      const imageUri = result.assets[0].uri;

      // Upload the image
      const publicUrl = await uploadImage(imageUri);

      if (publicUrl) {
        // Update user profile with new avatar URL
        const { error } = await updateProfile({
          avatar_url: publicUrl,
        });

        if (error) {
          toast.showErrorToast('Profile update failed', error.message);
          return null;
        } else {
          toast.showSuccessToast('Profile updated', 'Your profile photo has been updated');
          return publicUrl;
        }
      }

      return null;
    } catch (error) {
      console.error('Error picking/uploading image:', error);
      toast.showErrorToast('Error', 'Failed to update profile photo');
      return null;
    } finally {
      setUploading(false);
    }
  };

  const takePhoto = async () => {
    // Request camera permissions
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      toast.showErrorToast('Permission needed', 'Camera permissions are required to take photos');
      return null;
    }

    setUploading(true);

    try {
      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 1.0, // Start with highest quality, we'll compress later
      });

      if (result.canceled) {
        setUploading(false);
        return null;
      }

      const imageUri = result.assets[0].uri;

      // Upload the image
      const publicUrl = await uploadImage(imageUri);

      if (publicUrl) {
        // Update user profile with new avatar URL
        const { error } = await updateProfile({
          avatar_url: publicUrl,
        });

        if (error) {
          toast.showErrorToast('Profile update failed', error.message);
          return null;
        } else {
          toast.showSuccessToast('Profile updated', 'Your profile photo has been updated');
          return publicUrl;
        }
      }

      return null;
    } catch (error) {
      console.error('Error taking/uploading photo:', error);
      toast.showErrorToast('Error', 'Failed to update profile photo');
      return null;
    } finally {
      setUploading(false);
    }
  };

  return {
    uploading,
    pickAndUploadImage,
    takePhoto,
    uploadImage,
  };
}
