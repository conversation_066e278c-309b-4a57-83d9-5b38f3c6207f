import { useState, useCallback } from 'react';

export type UpgradeReason = 'manual' | 'feature_limit' | 'prayer_limit' | 'ad_shown';

interface UsePremiumReturn {
  isPremium: boolean;
  showUpgradeModal: boolean;
  upgradeReason: UpgradeReason;
  triggerUpgrade: (reason?: UpgradeReason) => void;
  closeUpgradeModal: () => void;
  upgradeToPremium: () => void;
  checkFeatureAccess: (feature: string) => boolean;
}

const FREE_LIMITS = {
  prayers_per_month: 10,
  broadcasting: false,
  listening_to_broadcasts: false,
};

export function usePremium(initialPremiumState = false): UsePremiumReturn {
  const [isPremium, setIsPremium] = useState(initialPremiumState);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [upgradeReason, setUpgradeReason] = useState<UpgradeReason>('manual');

  const triggerUpgrade = useCallback((reason: UpgradeReason = 'manual') => {
    setUpgradeReason(reason);
    setShowUpgradeModal(true);
  }, []);

  const closeUpgradeModal = useCallback(() => {
    setShowUpgradeModal(false);
  }, []);

  const upgradeToPremium = useCallback(() => {
    setIsPremium(true);
    setShowUpgradeModal(false);
  }, []);

  const checkFeatureAccess = useCallback(
    (feature: string): boolean => {
      if (isPremium) return true;
      const mockUsage = { prayers_this_month: 8 };
      switch (feature) {
        case 'create_prayer':
          return mockUsage.prayers_this_month < FREE_LIMITS.prayers_per_month;
        case 'broadcasting':
          return FREE_LIMITS.broadcasting;
        case 'listen_to_broadcasts':
          return FREE_LIMITS.listening_to_broadcasts;
        default:
          return true;
      }
    },
    [isPremium],
  );

  return {
    isPremium,
    showUpgradeModal,
    upgradeReason,
    triggerUpgrade,
    closeUpgradeModal,
    upgradeToPremium,
    checkFeatureAccess,
  };
}
