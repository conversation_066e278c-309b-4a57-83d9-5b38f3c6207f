import Toast from 'react-native-toast-message';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface ToastOptions {
  title: string;
  message?: string;
  duration?: number;
  position?: 'top' | 'bottom';
}

export const useToast = () => {
  const insets = useSafeAreaInsets();
  const topOffset = Math.max(80, (insets?.top || 0) + 20);
  const showToast = (
    type: 'success' | 'error' | 'info' | 'warning' | 'prayer' | 'broadcast',
    options: ToastOptions,
  ) => {
    Toast.show({
      type: type as any,
      text1: options.title,
      text2: options.message,
      position: options.position || 'top',
      visibilityTime: options.duration || 4000,
      autoHide: true,
      topOffset,
    });
  };

  const showSuccessToast = (title: string, message?: string) => {
    Toast.show({
      type: 'success' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset,
    });
  };

  const showErrorToast = (title: string, message?: string) => {
    Toast.show({
      type: 'error' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 5000,
      autoHide: true,
      topOffset,
    });
  };

  const showInfoToast = (title: string, message?: string) => {
    Toast.show({
      type: 'info' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset,
    });
  };

  const showWarningToast = (title: string, message?: string) => {
    Toast.show({
      type: 'warning' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset,
    });
  };

  const showPrayerToast = (title: string, message?: string) => {
    Toast.show({
      type: 'prayer' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset,
    });
  };

  const showBroadcastToast = (title: string, message?: string) => {
    Toast.show({
      type: 'broadcast' as any,
      text1: title,
      text2: message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset,
    });
  };

  const success = (options: ToastOptions) => showToast('success', options);
  const error = (options: ToastOptions) => showToast('error', options);
  const info = (options: ToastOptions) => showToast('info', options);
  const warning = (options: ToastOptions) => showToast('warning', options);
  const prayer = (options: ToastOptions) => showToast('prayer', options);
  const broadcast = (options: ToastOptions) => showToast('broadcast', options);

  const hide = () => Toast.hide();

  return {
    showSuccessToast,
    showErrorToast,
    showInfoToast,
    showWarningToast,
    showPrayerToast,
    showBroadcastToast,
    success,
    error,
    info,
    warning,
    prayer,
    broadcast,
    hide,
  };
};

// Note: Removed unused named convenience exports to reduce surface area.
// Prefer using the hook: const toast = useToast(); toast.showSuccessToast(...)
