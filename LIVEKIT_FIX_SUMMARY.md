# LiveKit Authentication Fix Summary

## ✅ What I Fixed

### 1. **Replaced Mock Token Generation**

- **Before**: Using `mock-token-${roomName}-${participantName}` (invalid)
- **After**: Proper JWT token generation via Supabase Edge Function using LiveKit SDK

### 2. **Created Secure Backend Token Generation**

- **New**: `livekit-token` Supabase Edge Function
- **Security**: API credentials stored securely as Supabase secrets (not in client code)
- **Authentication**: Requires user to be logged in to generate tokens

### 3. **Enhanced Error Handling & Logging**

- Added detailed console logs for debugging
- Better error messages for troubleshooting
- Clear success/failure indicators

### 4. **Created Setup Documentation**

- `scripts/setup-livekit.md` - Complete setup guide
- `scripts/test-livekit-token.js` - Test script for verification

## 🛠️ What You Need to Do Next

### Step 1: Get LiveKit Credentials

1. Go to [LiveKit Cloud](https://cloud.livekit.io)
2. Create/select a project
3. Copy your WebSocket URL, API Key, and API Secret

### Step 2: Set Environment Variables

Create a `.env` file in your project root:

```env
EXPO_PUBLIC_LIVEKIT_URL=wss://your-project.livekit.cloud
EXPO_PUBLIC_LIVEKIT_API_KEY=your-api-key
EXPO_PUBLIC_LIVEKIT_API_SECRET=your-api-secret
```

### Step 3: Configure Supabase Edge Function

```bash
# Set the LiveKit secrets for the Edge Function
npx supabase secrets set LIVEKIT_API_KEY=your-api-key --project-ref sbtpfbqjsuhguklhfgzz
npx supabase secrets set LIVEKIT_API_SECRET=your-api-secret --project-ref sbtpfbqjsuhguklhfgzz
npx supabase secrets set LIVEKIT_URL=wss://your-project.livekit.cloud --project-ref sbtpfbqjsuhguklhfgzz
```

### Step 4: Test the Fix

1. Build a development app (not Expo Go):
   ```bash
   npx expo prebuild --clean
   npx expo run:ios  # or expo run:android
   ```
2. Log in to your app
3. Create a prayer and try broadcasting
4. Look for these console messages:
   - ✅ Generated LiveKit token successfully
   - 🔗 Connecting to LiveKit: ...
   - ✅ Room created successfully: ...

## 🔍 Error Resolution

### Before (Your Original Errors):

```
❌ Failed to start broadcast: [ConnectionError: could not establish signal connection: invalid authorization token]
⚠️ Using mock token - implement proper token generation on your backend
⚠️ websocket closed {"code": 1006, "reason": "Received bad response code from server: 401."}
```

### After (Expected Success Messages):

```
✅ Generated LiveKit token successfully
🔗 Connecting to LiveKit: wss://your-project.livekit.cloud
✅ Room created successfully: prayer-broadcast-123
🎙️ Broadcast started for prayer: 123
```

## 📁 Files Modified

1. **`lib/livekit.ts`** - Updated token generation to use Supabase Edge Function
2. **`supabase/functions/livekit-token/index.ts`** - New Edge Function for secure token generation
3. **`scripts/setup-livekit.md`** - Setup guide
4. **`scripts/test-livekit-token.js`** - Test script

## 🎯 Key Benefits

- **Security**: API credentials never exposed in client code
- **Authentication**: Only logged-in users can generate tokens
- **Scalability**: Server-side token generation can handle any number of users
- **Reliability**: Proper JWT tokens that LiveKit servers accept
- **Debugging**: Clear logging to troubleshoot issues

## 🚨 Important Notes

1. **Development Build Required**: LiveKit doesn't work in Expo Go
2. **User Authentication Required**: Users must be logged in to generate tokens
3. **Environment Variables**: Must be set in both client (.env) and server (Supabase secrets)
4. **LiveKit Account**: You need a LiveKit Cloud account or self-hosted instance

Follow the setup guide in `scripts/setup-livekit.md` for detailed instructions!
