# Design System Examples

Practical examples of using the prayer app design system in different scenarios.

## Complete Component Examples

### 1. Prayer Card Component

```typescript
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { BlurView } from 'expo-blur';
import { createStyles, useDesignTokens, mixins } from '../lib/design';
import { Ionicons } from '@expo/vector-icons';

interface PrayerCardProps {
  title: string;
  description: string;
  isLive?: boolean;
  onPress: () => void;
}

const PrayerCard: React.FC<PrayerCardProps> = ({
  title,
  description,
  isLive,
  onPress
}) => {
  const tokens = useDesignTokens();

  const styles = createStyles((tokens) => ({
    container: {
      borderRadius: tokens.radius.sm,
      marginBottom: tokens.spacing.md,
      overflow: 'hidden',
    },
    card: {
      ...mixins.glassCard('light'),
      padding: tokens.spacing.lg,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: tokens.spacing.md,
    },
    title: {
      ...tokens.typography.prayer.title,
      color: tokens.colors.text.primary,
      flex: 1,
    },
    description: {
      ...tokens.typography.body.medium,
      color: tokens.colors.text.secondary,
      lineHeight: tokens.lineHeight.lg,
    },
    liveBadge: {
      ...tokens.semanticColors.status.live,
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: tokens.radius.xs,
      paddingHorizontal: tokens.spacing.sm,
      paddingVertical: tokens.spacing.xs,
      gap: tokens.spacing.xs,
    },
    liveDot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: tokens.semanticColors.status.live.dot,
    },
    liveText: {
      ...tokens.typography.caption.medium,
      fontWeight: '500',
      color: tokens.semanticColors.status.live.text,
    },
  }));

  return (
    <TouchableOpacity onPress={onPress} style={styles.container} activeOpacity={0.9}>
      <BlurView intensity={tokens.blur.strong} style={styles.card}>
        <View style={styles.header}>
          <Text style={styles.title}>{title}</Text>
          {isLive && (
            <View style={styles.liveBadge}>
              <View style={styles.liveDot} />
              <Text style={styles.liveText}>LIVE</Text>
            </View>
          )}
        </View>
        <Text style={styles.description}>{description}</Text>
      </BlurView>
    </TouchableOpacity>
  );
};
```

### 2. Form Field Component

```typescript
import React from 'react';
import { View, Text, TextInput, TextInputProps } from 'react-native';
import { BlurView } from 'expo-blur';
import { createStyles, useDesignTokens, mixins } from '../lib/design';

interface FormFieldProps extends TextInputProps {
  label: string;
  required?: boolean;
  subtitle?: string;
  characterCount?: { current: number; max: number };
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  required,
  subtitle,
  characterCount,
  style,
  ...textInputProps
}) => {
  const tokens = useDesignTokens();

  const styles = createStyles((tokens) => ({
    container: {
      marginBottom: tokens.spacing.lg,
      borderRadius: tokens.radius.xl,
      overflow: 'hidden',
    },
    content: {
      ...mixins.glassCard('light'),
      padding: tokens.spacing.lg,
    },
    label: {
      ...tokens.typography.heading.h4,
      color: tokens.colors.text.primary,
      marginBottom: tokens.spacing.sm,
    },
    required: {
      color: tokens.colors.error[400],
    },
    subtitle: {
      ...tokens.typography.body.small,
      color: tokens.colors.text.tertiary,
      marginBottom: tokens.spacing.md,
    },
    input: {
      backgroundColor: 'transparent',
      borderWidth: 0,
      color: tokens.colors.text.primary,
      ...tokens.typography.body.medium,
      paddingVertical: tokens.spacing.sm,
    },
    characterCount: {
      ...tokens.typography.caption.medium,
      color: tokens.colors.text.quaternary,
      textAlign: 'right',
      marginTop: tokens.spacing.sm,
    },
  }));

  return (
    <View style={styles.container}>
      <BlurView intensity={tokens.blur.medium} style={styles.content}>
        <Text style={styles.label}>
          {label} {required && <Text style={styles.required}>*</Text>}
        </Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        <TextInput
          style={[styles.input, style]}
          placeholderTextColor={tokens.colors.text.quaternary}
          {...textInputProps}
        />
        {characterCount && (
          <Text style={styles.characterCount}>
            {characterCount.current}/{characterCount.max}
          </Text>
        )}
      </BlurView>
    </View>
  );
};
```

### 3. Modal Component

```typescript
import React from 'react';
import { View, Text, Modal, TouchableOpacity, Dimensions } from 'react-native';
import { BlurView } from 'expo-blur';
import { createStyles, useDesignTokens, mixins } from '../lib/design';
import { Ionicons } from '@expo/vector-icons';
import Button from '../components/ui/Button';

interface CustomModalProps {
  visible: boolean;
  title: string;
  children: React.ReactNode;
  onClose: () => void;
  onConfirm?: () => void;
  confirmText?: string;
  confirmVariant?: 'primary' | 'destructive';
}

const CustomModal: React.FC<CustomModalProps> = ({
  visible,
  title,
  children,
  onClose,
  onConfirm,
  confirmText = 'Confirm',
  confirmVariant = 'primary',
}) => {
  const tokens = useDesignTokens();
  const { height } = Dimensions.get('window');

  const styles = createStyles((tokens) => ({
    overlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: tokens.spacing.lg,
    },
    container: {
      width: '100%',
      maxHeight: height * 0.8,
      borderRadius: tokens.radius['2xl'],
      overflow: 'hidden',
    },
    content: {
      ...mixins.glassCard('strong'),
      padding: tokens.spacing.lg,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: tokens.spacing.lg,
    },
    title: {
      ...tokens.typography.heading.h2,
      color: tokens.colors.text.primary,
      flex: 1,
    },
    closeButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: tokens.colors.surface.glassMedium,
      alignItems: 'center',
      justifyContent: 'center',
    },
    body: {
      marginBottom: tokens.spacing.lg,
    },
    actions: {
      flexDirection: 'row',
      gap: tokens.spacing.md,
    },
    actionButton: {
      flex: 1,
    },
  }));

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.container}>
          <BlurView intensity={tokens.blur.maximum} style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>{title}</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons
                  name="close"
                  size={20}
                  color={tokens.colors.text.tertiary}
                />
              </TouchableOpacity>
            </View>

            <View style={styles.body}>
              {children}
            </View>

            {onConfirm && (
              <View style={styles.actions}>
                <Button
                  title="Cancel"
                  variant="secondary"
                  onPress={onClose}
                  style={styles.actionButton}
                />
                <Button
                  title={confirmText}
                  variant={confirmVariant}
                  onPress={onConfirm}
                  style={styles.actionButton}
                />
              </View>
            )}
          </BlurView>
        </View>
      </View>
    </Modal>
  );
};
```

## Tailwind CSS Examples

### Using Custom Classes

```typescript
// Prayer card with Tailwind
<View className="bg-surface-glass-light border border-surface-glass-border rounded-sm p-lg mb-md">
  <View className="flex-row justify-between items-start mb-md">
    <Text className="text-text-primary text-xl font-semibold flex-1">
      Prayer Title
    </Text>
    <View className="bg-error-500/20 border border-error-500/30 rounded-xs px-sm py-xs flex-row items-center gap-xs">
      <View className="w-1.5 h-1.5 rounded-full bg-error-500" />
      <Text className="text-error-300 text-xs font-medium">LIVE</Text>
    </View>
  </View>
  <Text className="text-text-secondary text-base leading-7">
    Prayer description text here...
  </Text>
</View>
```

## Common Patterns

### 1. Glass Card Pattern

```typescript
const glassCardStyle = {
  ...mixins.glassCard('medium'),
  padding: tokens.spacing.lg,
  borderRadius: tokens.radius.sm,
};
```

### 2. Status Badge Pattern

```typescript
const statusBadge = (status: 'live' | 'answered' | 'scheduled') => ({
  ...tokens.semanticColors.status[status],
  flexDirection: 'row' as const,
  alignItems: 'center' as const,
  borderRadius: tokens.radius.xs,
  paddingHorizontal: tokens.spacing.sm,
  paddingVertical: tokens.spacing.xs,
  gap: tokens.spacing.xs,
});
```

### 3. Form Layout Pattern

```typescript
const formStyles = createStyles((tokens) => ({
  container: {
    ...mixins.layout.screenPadding,
    gap: tokens.spacing.lg,
  },
  section: {
    ...mixins.layout.section,
  },
  field: {
    ...mixins.glassCard('light'),
    padding: tokens.spacing.lg,
  },
}));
```

### 4. Button Group Pattern

```typescript
const buttonGroupStyles = createStyles((tokens) => ({
  container: {
    flexDirection: 'row',
    gap: tokens.spacing.md,
  },
  button: {
    flex: 1,
  },
}));

// Usage
<View style={buttonGroupStyles.container}>
  <Button title="Cancel" variant="secondary" style={buttonGroupStyles.button} />
  <Button title="Save" variant="primary" style={buttonGroupStyles.button} />
</View>
```

## Migration Examples

### Before (Old System)

```typescript
const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    borderRadius: 8,
    padding: 24,
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '500',
    color: 'white',
    fontFamily: 'Archivo-Medium',
    marginBottom: 8,
  },
  button: {
    backgroundColor: 'rgba(34, 197, 94, 0.2)',
    borderColor: 'rgba(34, 197, 94, 0.3)',
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
});
```

### After (New Design System)

```typescript
const styles = createStyles((tokens) => ({
  container: {
    ...mixins.glassCard('light'),
    marginBottom: tokens.spacing.md,
  },
  title: {
    ...tokens.typography.heading.h3,
    color: tokens.colors.text.primary,
    marginBottom: tokens.spacing.sm,
  },
  // Button replaced with Button component
}));

// In JSX
<Button
  title="Save"
  variant="primary"
  size="md"
  onPress={handleSave}
/>
```
