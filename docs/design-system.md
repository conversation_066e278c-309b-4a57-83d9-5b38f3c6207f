# Prayer App Design System

A comprehensive design system built with Tailwind CSS and React Native, providing consistent styling across the entire prayer app.

## Overview

The design system provides:

- **Centralized design tokens** for colors, spacing, typography, and effects
- **Tailwind CSS integration** with custom theme extensions
- **React Native StyleSheet compatibility**
- **Component variants** for consistent UI patterns
- **8px spacing system** with small border radius throughout

## Quick Start

### 1. Import Design Tokens

```typescript
// Import everything
import { useDesignTokens, createStyles, mixins, variants } from '../lib/design';

// Import specific tokens
import { colors, spacing, typography, blur } from '../lib/design/tokens';
```

### 2. Using with React Native StyleSheet

```typescript
import { createStyles, useDesignTokens } from '../lib/design';

const MyComponent = () => {
  const tokens = useDesignTokens();

  const styles = createStyles((tokens) => ({
    container: {
      backgroundColor: tokens.colors.background.primary,
      padding: tokens.spacing.lg,
      borderRadius: tokens.radius.sm,
    },
    title: {
      ...tokens.typography.heading.h2,
      color: tokens.colors.text.primary,
    },
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Hello World</Text>
    </View>
  );
};
```

### 3. Using with Tailwind Classes (NativeWind)

```typescript
// Use custom Tailwind classes
<View className="bg-surface-glass-light p-lg rounded-sm border border-surface-glass-border">
  <Text className="text-text-primary text-lg font-medium">Hello World</Text>
</View>
```

## Design Tokens

### Colors

#### Primary Palette

- `primary[50-900]` - Purple/blue theme colors
- `success[50-900]` - Green colors for positive actions
- `error[50-900]` - Red colors for destructive actions
- `warning[50-900]` - Orange colors for warnings
- `info[50-900]` - Blue colors for information
- `prayer[50-900]` - Pink colors for prayer-specific elements

#### Surface Colors (Glassmorphism)

- `surface.glassLight` - `rgba(255, 255, 255, 0.05)`
- `surface.glassMedium` - `rgba(255, 255, 255, 0.1)`
- `surface.glassStrong` - `rgba(255, 255, 255, 0.15)`
- `surface.glassBorder` - `rgba(255, 255, 255, 0.2)`

#### Text Colors

- `text.primary` - `#ffffff`
- `text.secondary` - `rgba(255, 255, 255, 0.8)`
- `text.tertiary` - `rgba(255, 255, 255, 0.6)`
- `text.quaternary` - `rgba(255, 255, 255, 0.4)`
- `text.disabled` - `rgba(255, 255, 255, 0.3)`

### Spacing (8px System)

```typescript
spacing = {
  xs: 4, // 0.5x
  sm: 8, // 1x (base)
  md: 16, // 2x
  lg: 24, // 3x
  xl: 32, // 4x
  '2xl': 40, // 5x
  '3xl': 48, // 6x
  '4xl': 64, // 8x
  '5xl': 80, // 10x
  '6xl': 96, // 12x
};
```

### Border Radius (Small Throughout)

```typescript
radius = {
  xs: 4,
  sm: 8, // Primary radius
  md: 8, // Keep consistent
  lg: 8, // Keep consistent
  xl: 12, // Slightly larger for special cases
  '2xl': 16, // For large components
  full: 9999, // Fully rounded
};
```

### Typography

#### Font Families

- `Roboto` - Primary body text
- `Archivo` - Headings and UI text
- `Antonio` - Display text (if needed)

#### Typography Scale

- `display.large/medium/small` - Large headings
- `heading.h1/h2/h3/h4` - Section headings
- `body.large/medium/small` - Body text
- `label.large/medium/small` - UI labels
- `button.large/medium/small` - Button text
- `caption.large/medium` - Helper text

### Effects

#### Blur Intensities

- `blur.light` - 16 (subtle effects)
- `blur.medium` - 24 (cards and modals)
- `blur.strong` - 40 (prominent elements)
- `blur.intense` - 60 (overlays)
- `blur.maximum` - 80 (strong separation)

#### Shadows

- `shadows.xs/sm/md/lg/xl` - Standard shadows
- `shadows.prayer/success/error/info` - Colored shadows

## Component Usage

### Buttons

```typescript
import Button from '../components/ui/Button';
import IconButton from '../components/ui/IconButton';

// Standard button
<Button
  title="Save Prayer"
  variant="primary"
  size="md"
  icon="heart"
  onPress={handleSave}
  fullWidth
/>

// Icon button
<IconButton
  icon="trash-outline"
  variant="destructive"
  size="md"
  onPress={handleDelete}
/>
```

### Button Variants

- `primary` - Green success actions
- `secondary` - Glass background
- `destructive` - Red destructive actions
- `prayer` - Purple prayer-specific actions

### Cards with Glassmorphism

```typescript
// Using mixins
const styles = createStyles((tokens) => ({
  card: {
    ...mixins.glassCard('medium'),
    padding: tokens.spacing.lg,
  },
}));

// Using variants
const styles = createStyles((tokens) => ({
  card: {
    ...variants.card.elevated,
    padding: tokens.spacing.lg,
  },
}));
```

## Best Practices

### 1. Always Use Design Tokens

❌ **Don't:**

```typescript
backgroundColor: 'rgba(255,255,255,0.1)',
padding: 16,
fontSize: 18,
```

✅ **Do:**

```typescript
backgroundColor: tokens.colors.surface.glassMedium,
padding: tokens.spacing.md,
...tokens.typography.body.large,
```

### 2. Use Semantic Colors

❌ **Don't:**

```typescript
color: '#22c55e',
```

✅ **Do:**

```typescript
color: tokens.colors.success[500],
// or
backgroundColor: tokens.semanticColors.button.primary.background,
```

### 3. Consistent Spacing

❌ **Don't:**

```typescript
marginBottom: 20,
gap: 12,
```

✅ **Do:**

```typescript
marginBottom: tokens.spacing.lg,
gap: tokens.spacing.md,
```

### 4. Typography Consistency

❌ **Don't:**

```typescript
fontSize: 16,
fontWeight: '600',
fontFamily: 'Roboto-Medium',
```

✅ **Do:**

```typescript
...tokens.typography.body.medium,
```

## Migration Guide

### From Old System to New

1. **Replace hardcoded colors:**

   ```typescript
   // Old
   backgroundColor: 'rgba(34, 197, 94, 0.2)',

   // New
   backgroundColor: tokens.semanticColors.button.primary.background,
   ```

2. **Replace spacing values:**

   ```typescript
   // Old
   padding: 24,
   margin: 16,

   // New
   padding: tokens.spacing.lg,
   margin: tokens.spacing.md,
   ```

3. **Replace typography:**

   ```typescript
   // Old
   fontSize: 18,
   fontWeight: '500',
   fontFamily: 'Archivo-Medium',

   // New
   ...tokens.typography.heading.h3,
   ```

## Tailwind Integration

The design system extends Tailwind with custom classes:

```typescript
// Custom spacing
className = 'p-lg m-md gap-sm';

// Custom colors
className = 'bg-surface-glass-medium text-text-primary border-surface-glass-border';

// Custom radius
className = 'rounded-sm'; // 8px throughout
```

## Component Library

### Available Components

- `Button` - Primary action buttons with variants
- `IconButton` - Icon-only buttons
- `Card` - Glass morphism cards (coming soon)
- `Input` - Form inputs (coming soon)
- `Modal` - Overlay modals (coming soon)

### Creating New Components

Always use the design system when creating new components:

```typescript
import { createStyles, useDesignTokens, mixins } from '../lib/design';

const MyComponent = () => {
  const styles = createStyles((tokens) => ({
    container: {
      ...mixins.glassCard('medium'),
      ...mixins.layout.screenPadding,
    },
    title: {
      ...tokens.typography.heading.h2,
      color: tokens.colors.text.primary,
    },
  }));

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Component Title</Text>
    </View>
  );
};
```
