# Development Build Guide

This app includes live prayer broadcasting features that require a **development build** and are not available in **Expo Go**.

## What works in Expo Go ✅

- All prayer features (create, view, like, comment)
- User authentication and profiles
- Real-time updates for likes and comments
- Prayer outcomes and sharing
- All navigation and UI features

## What requires a development build 🔧

- **Live prayer broadcasting** (audio streaming)
- **Joining live broadcasts**
- **Real-time audio communication**

## How to create a development build

### Option 1: EAS Build (Recommended)

```bash
# Install EAS CLI
npm install -g eas-cli

# Login to Expo
eas login

# Configure EAS
eas build:configure

# Build for iOS
eas build --platform ios

# Build for Android
eas build --platform android
```

### Option 2: Local Development Build

```bash
# Install dependencies
npx expo install --fix

# Prebuild native directories
npx expo prebuild --clean

# Create development build
npx expo run:ios
# or
npx expo run:android
```

### Troubleshooting CocoaPods Conflicts

If you encounter WebRTC framework conflicts:

```bash
# Clean everything
rm -rf ios android .expo node_modules

# Reinstall dependencies
npm install

# Prebuild with clean slate
npx expo prebuild --clean

# Build again
npx expo run:ios
```

## LiveKit Configuration

Once you have a development build:

1. **Get LiveKit credentials** from [LiveKit Cloud](https://cloud.livekit.io)
2. **Create environment file** `.env`:
   ```
   EXPO_PUBLIC_LIVEKIT_URL=wss://your-project.livekit.cloud
   EXPO_PUBLIC_LIVEKIT_API_KEY=your-api-key
   EXPO_PUBLIC_LIVEKIT_API_SECRET=your-api-secret
   ```
3. **Set up backend token generation** (see `BROADCAST_SETUP.md` for details)

## Testing Broadcasting

1. **Build the app** with development build
2. **Configure LiveKit** credentials
3. **Test locally** on physical devices
4. **Create a prayer** and tap "Broadcast"
5. **Grant microphone permission** when prompted
6. **Start broadcasting** your prayer audio live

## Why Development Builds?

- **Native modules**: LiveKit uses WebRTC which requires native iOS/Android modules
- **Permissions**: Microphone access requires native permission handling
- **Performance**: Real-time audio needs optimized native performance
- **App Store**: Production apps need development builds anyway

## Need Help?

- [Expo Development Builds Docs](https://docs.expo.dev/development/introduction/)
- [LiveKit React Native Docs](https://docs.livekit.io/realtime/client-sdks/react-native/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)

The app gracefully handles both Expo Go and development build environments! 🙏
