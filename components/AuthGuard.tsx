import React from 'react';
import { useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import VideoLoadingScreen from './VideoLoadingScreen';

interface AuthGuardProps {
  children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { user, loading } = useAuth();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    if (loading) return; // Wait for auth to initialize

    const currentPath = segments.join('/');
    const inAuthGroup = segments[0] === '(tabs)';
    const inAuthFlow = segments[0] === 'login' || segments[0] === 'signup';
    const onSplash = segments[0] === 'splash' || currentPath === '';
    const onOnboarding = segments[0] === 'onboarding';

    // Debug logging
    console.log('AuthGuard - User:', !!user, 'Path:', currentPath, 'Segments:', segments);

    if (!user && inAuthGroup) {
      // User is not authenticated but trying to access protected routes
      console.log('AuthGuard: Redirecting unauthenticated user to splash');
      router.replace('/splash');
    } else if (user && inAuthFlow) {
      // User is authenticated but on auth screens - go to prayer feed
      console.log('AuthGuard: Redirecting authenticated user from auth screens to tabs');
      router.replace('/(tabs)');
    } else if (user && onSplash && !onOnboarding) {
      // User is authenticated and on splash - go to prayer feed
      console.log('AuthGuard: Redirecting authenticated user from splash to tabs');
      router.replace('/(tabs)');
    }
  }, [user, segments, loading]);

  if (loading) {
    return <VideoLoadingScreen />;
  }

  return <>{children}</>;
}
