import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';

interface VideoLoaderProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export default function VideoLoader({
  size = 'medium',
  color = 'rgba(255, 255, 255, 0.8)',
}: VideoLoaderProps) {
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Rotation animation
    const rotate = () => {
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }).start(() => {
        rotateAnim.setValue(0);
        rotate();
      });
    };

    // Pulse animation
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };

    rotate();
    pulse();
  }, []);

  const spin = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const getSize = () => {
    switch (size) {
      case 'small':
        return 16;
      case 'medium':
        return 24;
      case 'large':
        return 32;
      default:
        return 24;
    }
  };

  const loaderSize = getSize();

  return (
    <Animated.View
      style={[
        styles.container,
        {
          width: loaderSize,
          height: loaderSize,
          transform: [{ rotate: spin }, { scale: pulseAnim }],
        },
      ]}
    >
      <View
        style={[
          styles.outerRing,
          {
            width: loaderSize,
            height: loaderSize,
            borderRadius: loaderSize / 2,
            borderColor: color,
          },
        ]}
      >
        <View
          style={[
            styles.innerDot,
            {
              width: loaderSize * 0.3,
              height: loaderSize * 0.3,
              borderRadius: (loaderSize * 0.3) / 2,
              backgroundColor: color,
            },
          ]}
        />
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  outerRing: {
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    backgroundColor: 'transparent',
  },
  innerDot: {
    shadowColor: '#fff',
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.5,
    shadowRadius: 3,
  },
});
