import { View, Text, StyleSheet } from 'react-native';
import { useState, useEffect } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { getPrayerCommentCount, subscribeToCommentCount } from '../utils/commentUtils';

interface CommentCountProps {
  prayerId: string;
  initialCount?: number;
  showIcon?: boolean;
  style?: any;
  textStyle?: any;
  iconColor?: string;
  iconSize?: number;
}

export default function CommentCount({
  prayerId,
  initialCount = 0,
  showIcon = true,
  style,
  textStyle,
  iconColor = 'rgba(255,255,255,0.6)',
  iconSize = 16,
}: CommentCountProps) {
  const [count, setCount] = useState(initialCount);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Fetch current count
    const fetchCount = async () => {
      setLoading(true);
      const currentCount = await getPrayerCommentCount(prayerId);
      setCount(currentCount);
      setLoading(false);
    };

    fetchCount();

    // Subscribe to real-time updates
    const unsubscribe = subscribeToCommentCount(prayerId, (newCount) => {
      setCount(newCount);
    });

    return unsubscribe;
  }, [prayerId]);

  return (
    <View style={[styles.container, style]}>
      {showIcon && <Ionicons name="chatbubble-outline" size={iconSize} color={iconColor} />}
      <Text style={[styles.text, textStyle]}>{loading ? '...' : count.toString()}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  text: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
  },
});
