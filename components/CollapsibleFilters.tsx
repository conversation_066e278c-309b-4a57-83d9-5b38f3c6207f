import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import Animated, { 
  FadeInUp, 
  useAnimatedStyle, 
  useSharedValue, 
  withTiming,
  interpolate,
  Extrapolate
} from 'react-native-reanimated';
import Ionicons from '@expo/vector-icons/Ionicons';
import { componentStyles, textStyles, spacing } from '../lib/design';

interface CollapsibleFiltersProps {
  activeFilter: 'all' | 'live' | 'week' | 'recent';
  activeCategory: string;
  onFilterChange: (filter: 'all' | 'live' | 'week' | 'recent') => void;
  onCategoryChange: (category: string) => void;
  categories: string[];
}

export default function CollapsibleFilters({
  activeFilter,
  activeCategory,
  onFilterChange,
  onCategoryChange,
  categories,
}: CollapsibleFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const expandAnimation = useSharedValue(0);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    expandAnimation.value = withTiming(isExpanded ? 0 : 1, { duration: 300 });
  };

  const animatedStyle = useAnimatedStyle(() => {
    const height = interpolate(
      expandAnimation.value,
      [0, 1],
      [0, 180], // More accurate height when expanded
      Extrapolate.CLAMP
    );

    return {
      height,
      opacity: expandAnimation.value,
    };
  });

  const chevronStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      expandAnimation.value,
      [0, 1],
      [0, 180],
      Extrapolate.CLAMP
    );
    
    return {
      transform: [{ rotate: `${rotation}deg` }],
    };
  });

  // Get display names for active filters
  const getActiveFilterDisplay = () => {
    const timeFilter = activeFilter === 'all' ? 'All' : 
                     activeFilter === 'live' ? 'Live Now' :
                     activeFilter === 'week' ? 'This Week' : 'Most Recent';
    
    const categoryFilter = activeCategory === 'all' ? 'All Categories' : activeCategory;
    
    return `${timeFilter}, ${categoryFilter}`;
  };

  const timeFilters = [
    { key: 'all', label: 'All' },
    { key: 'live', label: 'Live Now' },
    { key: 'week', label: 'This Week' },
    { key: 'recent', label: 'Most Recent' },
  ] as const;

  return (
    <Animated.View entering={FadeInUp.delay(200).duration(600)}>
      <View style={{
        marginHorizontal: spacing.md,
        marginBottom: spacing.sm,
      }}>
        {/* Collapsed Header */}
        <TouchableOpacity
          onPress={toggleExpanded}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingVertical: spacing.sm,
          }}
          activeOpacity={0.7}
          accessibilityRole="button"
          accessibilityLabel={isExpanded ? "Collapse filters" : "Expand filters"}
          accessibilityHint="Tap to show or hide filter options"
        >
          <View style={{ flex: 1 }}>
            <Text style={[textStyles.chipText, {
              color: 'white',
              fontWeight: '500',
              marginBottom: 2,
            }]}>
              Filters
            </Text>
            <Text style={[textStyles.caption, {
              color: 'rgba(255,255,255,0.7)',
              fontSize: 13,
            }]}>
              {getActiveFilterDisplay()}
            </Text>
          </View>

          <Animated.View style={chevronStyle}>
            <Ionicons
              name="chevron-down"
              size={20}
              color="rgba(255,255,255,0.8)"
            />
          </Animated.View>
        </TouchableOpacity>

        {/* Expanded Content */}
        <Animated.View style={[animatedStyle, { overflow: 'hidden' }]}>
          <View style={{
            paddingBottom: spacing.md,
            backgroundColor: 'rgba(255,255,255,0.1)',
            borderRadius: 8,
            padding: spacing.md,
            marginTop: spacing.xs,
          }}>
            {/* Time Filters Section */}
            <View style={{ marginBottom: spacing.md }}>
              <Text style={[textStyles.chipText, {
                color: 'white',
                fontWeight: '600',
                marginBottom: spacing.sm,
                fontSize: 14,
              }]}>
                Time
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingVertical: spacing.xs }}
              >
                {timeFilters.map((filter, index) => (
                  <TouchableOpacity
                    key={filter.key}
                    style={{ marginRight: 12 }}
                    activeOpacity={0.8}
                    onPress={() => onFilterChange(filter.key)}
                    accessibilityRole="button"
                    accessibilityLabel={`Filter by ${filter.label}`}
                    accessibilityState={{ selected: activeFilter === filter.key }}
                  >
                    <View style={[
                      componentStyles.chip, 
                      componentStyles.chipSmall,
                      activeFilter === filter.key && componentStyles.chipActive
                    ]}>
                      <Text style={[
                        textStyles.chipText,
                        activeFilter === filter.key && { color: '#a78bfa', fontWeight: '600' }
                      ]}>
                        {filter.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Category Filters Section */}
            <View>
              <Text style={[textStyles.chipText, {
                color: 'white',
                fontWeight: '600',
                marginBottom: spacing.sm,
                fontSize: 14,
              }]}>
                Category
              </Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{ paddingVertical: spacing.xs }}
              >
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={{ marginRight: spacing.sm }}
                    activeOpacity={0.8}
                    onPress={() => onCategoryChange(category)}
                    accessibilityRole="button"
                    accessibilityLabel={`Filter by ${category === 'all' ? 'All Categories' : category}`}
                    accessibilityState={{ selected: activeCategory === category }}
                  >
                    <View style={[
                      componentStyles.chip, 
                      componentStyles.chipSmall,
                      activeCategory === category && {
                        backgroundColor: 'rgba(236, 72, 153, 0.3)',
                        borderColor: 'rgba(236, 72, 153, 0.5)'
                      }
                    ]}>
                      <Text style={[
                        textStyles.chipText,
                        activeCategory === category && { color: '#f472b6', fontWeight: '600' }
                      ]}>
                        {category === 'all' ? 'All Categories' : category}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Animated.View>
      </View>
    </Animated.View>
  );
}
