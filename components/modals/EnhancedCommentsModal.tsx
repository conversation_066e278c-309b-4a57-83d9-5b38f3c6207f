import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useState, useEffect } from 'react';
import { useRouter } from 'expo-router';
import Ionicons from '@expo/vector-icons/Ionicons';
import { BlurView } from 'expo-blur';
import {
  supabase,
  PrayerWithProfile,
  PrayerCommentWithProfile,
  PrayerCommentInsert,
  CommentLikeInsert,
  CommentReportInsert,
  ReportReason,
} from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { useToast } from '../../hooks/useToast';
import { formatRelativeTime } from '../../utils/dateUtils';
import { createStyles, useDesignTokens, surfaces } from '../../lib/design';
import ProfileImage from '../ProfileImage';
import VideoLoadingScreen from '../VideoLoadingScreen';
import { ActivityIndicator } from 'react-native';

interface EnhancedCommentsModalProps {
  isOpen: boolean;
  onClose: () => void;
  prayer: PrayerWithProfile;
}

interface ReplyState {
  commentId: string | null;
  text: string;
}

interface CommentItemProps {
  comment: PrayerCommentWithProfile;
  onReply: (commentId: string) => void;
  onLike: (commentId: string) => void;
  onReport: (commentId: string) => void;
  currentUserId?: string;
  level?: number;
}

function CommentItem(props: CommentItemProps) {
  const { comment, onReply, onLike, onReport, currentUserId, level = 0 } = props;
  const router = useRouter();
  const tokens = useDesignTokens();
  const [showReplies, setShowReplies] = useState(false);

  // Get user display name and initial
  const getUserDisplayName = () => {
    return (
      comment.user_profiles?.display_name || comment.user_profiles?.full_name || 'Anonymous User'
    );
  };



  const isOwnComment = currentUserId === comment.user_id;
  const canReply = level < 1; // Only allow replies to top-level comments (max 2 levels)

  return (
    <View style={[styles.commentContainer, level > 0 && styles.replyContainer]}>
      <View style={styles.commentRow}>
        <ProfileImage
          avatarUrl={comment.user_profiles?.avatar_url}
          displayName={getUserDisplayName()}
          fullName={comment.user_profiles?.full_name}
          size={level > 0 ? 28 : 32}
          style={styles.commentAvatar}
        />

        <View style={styles.commentContent}>
          <View style={styles.commentHeader}>
            <TouchableOpacity
              onPress={() => {
                if (comment.user_profiles?.id) {
                  if (comment.user_profiles.id === currentUserId) {
                    router.push('/(tabs)/profile');
                  } else {
                    router.push({
                      pathname: '/user-profile',
                      params: { userId: comment.user_profiles.id },
                    });
                  }
                }
              }}
            >
              <Text style={styles.authorName}>{getUserDisplayName()}</Text>
            </TouchableOpacity>
            <Text style={styles.timestamp}>• {formatRelativeTime(comment.created_at)}</Text>
          </View>

          <Text style={styles.commentText}>{comment.content}</Text>

          <View style={styles.commentActions}>
            <TouchableOpacity style={styles.actionButton} onPress={() => onLike(comment.id)}>
              <Ionicons
                name={comment.is_liked ? 'heart' : 'heart-outline'}
                size={14}
                color={comment.is_liked ? tokens.colors.prayer[400] : tokens.colors.text.tertiary}
              />
              {(comment.like_count || 0) > 0 && (
                <Text style={[styles.actionText, comment.is_liked && styles.actionTextLiked]}>
                  {String(comment.like_count || 0)}
                </Text>
              )}
            </TouchableOpacity>

            {canReply && (
              <TouchableOpacity style={styles.actionButton} onPress={() => onReply(comment.id)}>
                <Text style={styles.actionText}>Reply</Text>
              </TouchableOpacity>
            )}

            {!isOwnComment && (
              <TouchableOpacity style={styles.actionButton} onPress={() => onReport(comment.id)}>
                <Text style={styles.actionText}>Report</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Show/Hide Replies */}
          {(comment.reply_count || 0) > 0 && (
            <TouchableOpacity
              style={styles.showRepliesButton}
              onPress={() => setShowReplies(!showReplies)}
            >
              <Ionicons
                name={showReplies ? 'chevron-up' : 'chevron-down'}
                size={12}
                color={tokens.colors.text.tertiary}
              />
              <Text style={styles.showRepliesText}>
                {showReplies ? 'Hide' : 'Show'} {String(comment.reply_count || 0)}{' '}
                {(comment.reply_count || 0) === 1 ? 'reply' : 'replies'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Replies */}
      {showReplies && comment.replies && comment.replies.length > 0 && (
        <View style={styles.repliesContainer}>
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              onReply={onReply}
              onLike={onLike}
              onReport={onReport}
              currentUserId={currentUserId}
              level={level + 1}
            />
          ))}
        </View>
      )}
    </View>
  );
}

function ReportModal({
  isOpen,
  onClose,
  onSubmit,
}: {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (reason: ReportReason, description?: string) => void;
}) {
  const [selectedReason, setSelectedReason] = useState<ReportReason>('spam');
  const [description, setDescription] = useState('');
  const tokens = useDesignTokens();

  const reasons: { value: ReportReason; label: string }[] = [
    { value: 'spam', label: 'Spam or unwanted content' },
    { value: 'inappropriate', label: 'Inappropriate content' },
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'false_information', label: 'False information' },
    { value: 'other', label: 'Other' },
  ];

  const handleSubmit = () => {
    onSubmit(selectedReason, description.trim() || undefined);
    setDescription('');
    onClose();
  };

  return (
    <Modal visible={isOpen} animationType="slide" presentationStyle="pageSheet">
      <BlurView intensity={tokens.blur.maximum} style={styles.reportModalContainer}>
        <View style={styles.reportModalHeader}>
          <Text style={styles.reportModalTitle}>Report Comment</Text>
          <TouchableOpacity onPress={onClose} style={styles.reportCloseButton}>
            <Ionicons name="close" size={20} color={tokens.colors.text.secondary} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.reportModalContent}>
          <Text style={styles.reportModalSubtitle}>Why are you reporting this comment?</Text>

          {reasons.map((reason) => (
            <TouchableOpacity
              key={reason.value}
              style={[
                styles.reasonOption,
                selectedReason === reason.value && styles.reasonOptionSelected,
              ]}
              onPress={() => setSelectedReason(reason.value)}
            >
              <Text
                style={[
                  styles.reasonText,
                  selectedReason === reason.value && styles.reasonTextSelected,
                ]}
              >
                {reason.label}
              </Text>
              {selectedReason === reason.value && (
                <Ionicons name="checkmark" size={18} color={tokens.colors.success[400]} />
              )}
            </TouchableOpacity>
          ))}

          <Text style={styles.descriptionLabel}>Additional details (optional)</Text>
          <TextInput
            value={description}
            onChangeText={setDescription}
            placeholder="Provide more context about why you're reporting this comment..."
            placeholderTextColor={tokens.colors.text.tertiary}
            style={styles.descriptionInput}
            multiline
            maxLength={500}
          />
        </ScrollView>

        <View style={styles.reportModalActions}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <Text style={styles.submitButtonText}>Submit Report</Text>
          </TouchableOpacity>
        </View>
      </BlurView>
    </Modal>
  );
}

export default function EnhancedCommentsModal({
  isOpen,
  onClose,
  prayer,
}: EnhancedCommentsModalProps) {
  const [newComment, setNewComment] = useState('');
  const [comments, setComments] = useState<PrayerCommentWithProfile[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [replyState, setReplyState] = useState<ReplyState>({ commentId: null, text: '' });
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportingCommentId, setReportingCommentId] = useState<string | null>(null);
  const { user, userProfile } = useAuth();
  const toast = useToast();
  const tokens = useDesignTokens();

  // Real-time subscription for comments
  useEffect(() => {
    if (!isOpen || !prayer?.id) return;

    console.log('🔌 Setting up realtime subscription for comments:', prayer.id);

    const commentsChannel = supabase
      .channel(`comments-${prayer.id}`, {
        config: {
          broadcast: { self: false },
        },
      })
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'prayer_comments',
          filter: `prayer_id=eq.${prayer.id}`,
        },
        (payload) => {
          console.log('🔄 Comment change:', payload);
          setTimeout(() => fetchComments(false), 100);
        },
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'comment_likes',
        },
        (payload) => {
          console.log('🔄 Comment like change:', payload);
          setTimeout(() => fetchComments(false), 100);
        },
      )
      .subscribe((status) => {
        console.log('🔌 Comments subscription status:', status);
      });

    return () => {
      console.log('🔌 Removing comments subscription...');
      supabase.removeChannel(commentsChannel);
    };
  }, [isOpen, prayer?.id]);

  // Fetch comments when modal opens
  useEffect(() => {
    if (isOpen && prayer?.id) {
      fetchComments();
    }
  }, [isOpen, prayer?.id]);

  const fetchComments = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    try {
      // Fetch top-level comments with user profiles and like status
      const { data: topLevelComments, error } = await supabase
        .from('prayer_comments')
        .select(
          `
          *,
          user_profiles(*),
          comment_likes(user_id)
        `,
        )
        .eq('prayer_id', prayer.id)
        .is('parent_id', null)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Fetch replies for each top-level comment
      const commentsWithReplies = await Promise.all(
        (topLevelComments || []).map(async (comment) => {
          const { data: replies, error: repliesError } = await supabase
            .from('prayer_comments')
            .select(
              `
              *,
              user_profiles(*),
              comment_likes(user_id)
            `,
            )
            .eq('parent_id', comment.id)
            .order('created_at', { ascending: true });

          if (repliesError) throw repliesError;

          return {
            ...comment,
            replies:
              replies?.map((reply) => ({
                ...reply,
                is_liked: user
                  ? reply.comment_likes?.some((like: any) => like.user_id === user.id)
                  : false,
              })) || [],
            is_liked: user
              ? comment.comment_likes?.some((like: any) => like.user_id === user.id)
              : false,
          };
        }),
      );

      setComments(commentsWithReplies as PrayerCommentWithProfile[]);
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.showErrorToast('Error', 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (parentId?: string) => {
    const text = parentId ? replyState.text : newComment;
    if (!text.trim() || !user || submitting) return;

    setSubmitting(true);
    try {
      const commentData: PrayerCommentInsert = {
        prayer_id: prayer.id,
        user_id: user.id,
        content: text.trim(),
        parent_id: parentId || null,
      };

      const { error } = await supabase.from('prayer_comments').insert(commentData);

      if (error) throw error;

      // Reset input
      if (parentId) {
        setReplyState({ commentId: null, text: '' });
      } else {
        setNewComment('');
      }

      toast.showSuccessToast('Success', parentId ? 'Reply added' : 'Comment added');
      setTimeout(() => fetchComments(false), 100);
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.showErrorToast('Error', 'Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    if (!user) return;

    try {
      // Check if already liked
      const { data: existingLike } = await supabase
        .from('comment_likes')
        .select('id')
        .eq('comment_id', commentId)
        .eq('user_id', user.id)
        .single();

      if (existingLike) {
        // Unlike
        const { error } = await supabase
          .from('comment_likes')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Like
        const likeData: CommentLikeInsert = {
          comment_id: commentId,
          user_id: user.id,
        };

        const { error } = await supabase.from('comment_likes').insert(likeData);

        if (error) throw error;
      }

      // Optimistic update
      setComments((prevComments) =>
        prevComments.map((comment) => {
          if (comment.id === commentId) {
            return {
              ...comment,
              is_liked: !existingLike,
              like_count: (comment.like_count || 0) + (existingLike ? -1 : 1),
            };
          }
          if (comment.replies) {
            return {
              ...comment,
              replies: comment.replies.map((reply) =>
                reply.id === commentId
                  ? {
                      ...reply,
                      is_liked: !existingLike,
                      like_count: (reply.like_count || 0) + (existingLike ? -1 : 1),
                    }
                  : reply,
              ),
            };
          }
          return comment;
        }),
      );

      setTimeout(() => fetchComments(false), 100);
    } catch (error) {
      console.error('Error toggling comment like:', error);
      toast.showErrorToast('Error', 'Failed to update like');
    }
  };

  const handleReportComment = (commentId: string) => {
    setReportingCommentId(commentId);
    setShowReportModal(true);
  };

  const handleSubmitReport = async (reason: ReportReason, description?: string) => {
    if (!user || !reportingCommentId) return;

    try {
      const reportData: CommentReportInsert = {
        comment_id: reportingCommentId,
        reporter_id: user.id,
        reason,
        description,
      };

      const { error } = await supabase.from('comment_reports').insert(reportData);

      if (error) throw error;

      toast.showSuccessToast('Report Submitted', 'Thank you for helping keep our community safe');
      setReportingCommentId(null);
    } catch (error) {
      console.error('Error submitting report:', error);
      toast.showErrorToast('Error', 'Failed to submit report');
    }
  };

  const handleReply = (commentId: string) => {
    setReplyState({ commentId, text: '' });
  };

  // Get user display name
  const getUserDisplayName = () => {
    return (
      prayer.user_profiles?.display_name || prayer.user_profiles?.full_name || 'Anonymous User'
    );
  };

  return (
    <>
      <Modal visible={isOpen} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          {/* Gradient Background */}
          <View style={styles.gradientBackground} />

          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerHandle} />
            <View style={styles.headerContent}>
              <Text style={styles.headerTitle}>Prayer Responses</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={20} color={tokens.colors.text.primary} />
              </TouchableOpacity>
            </View>
          </View>

          {/* Prayer Info */}
          <View style={styles.prayerInfoContainer}>
            <ProfileImage
              avatarUrl={prayer.user_profiles?.avatar_url}
              displayName={getUserDisplayName()}
              fullName={prayer.user_profiles?.full_name}
              size={32}
              style={styles.prayerAvatar}
            />
            <View style={styles.prayerInfoText}>
              <Text style={styles.prayerTitle} numberOfLines={2}>
                {prayer.title}
              </Text>
              <Text style={styles.prayerHost}>
                by {getUserDisplayName()} • {prayer.comment_count || 0} responses
              </Text>
            </View>
          </View>

          {/* Comments List */}
          <ScrollView
            style={styles.commentsList}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={styles.commentsContent}
          >
            {loading ? (
              <VideoLoadingScreen
                loading={loading}
                showText={true}
                loadingText="Loading responses..."
              />
            ) : comments.length === 0 ? (
              <View style={styles.emptyContainer}>
                <View style={styles.emptyIconContainer}>
                  <Ionicons
                    name="chatbubble-outline"
                    size={20}
                    color={tokens.colors.text.tertiary}
                  />
                </View>
                <Text style={styles.emptyText}>No responses yet</Text>
                <Text style={styles.emptySubtext}>
                  Be the first to share words of encouragement
                </Text>
              </View>
            ) : (
              comments.map((comment) => (
                <View key={comment.id}>
                  <CommentItem
                    comment={comment}
                    onReply={handleReply}
                    onLike={handleLikeComment}
                    onReport={handleReportComment}
                    currentUserId={user?.id}
                  />

                  {/* Reply Input */}
                  {replyState.commentId === comment.id && (
                    <View style={styles.replyInputContainer}>
                      <View style={styles.replyInputRow}>
                        <ProfileImage
                          avatarUrl={userProfile?.avatar_url}
                          displayName={userProfile?.display_name}
                          fullName={userProfile?.full_name}
                          size={24}
                          style={styles.replyAvatar}
                        />
                        <View style={styles.replyInputWrapper}>
                          <TextInput
                            value={replyState.text}
                            onChangeText={(text) => setReplyState((prev) => ({ ...prev, text }))}
                            placeholder={`Reply to ${comment.user_profiles?.display_name || comment.user_profiles?.full_name || 'Anonymous User'}...`}
                            placeholderTextColor={tokens.colors.text.tertiary}
                            style={styles.replyTextInput}
                            multiline={replyState.text.length > 50}
                            maxLength={500}
                            editable={!submitting}
                            autoFocus
                          />
                          <View style={styles.replyActions}>
                            <TouchableOpacity
                              onPress={() => setReplyState({ commentId: null, text: '' })}
                              style={styles.cancelReplyButton}
                            >
                              <Text style={styles.cancelReplyText}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              style={[
                                styles.replySubmitButton,
                                (!replyState.text.trim() || submitting) &&
                                  styles.replySubmitButtonDisabled,
                              ]}
                              onPress={() => handleSubmitComment(replyState.commentId!)}
                              disabled={!replyState.text.trim() || submitting}
                            >
                              {submitting ? (
                                <ActivityIndicator size="small" color="white" />
                              ) : (
                                <Ionicons name="send" size={14} color="white" />
                              )}
                            </TouchableOpacity>
                          </View>
                        </View>
                      </View>
                    </View>
                  )}
                </View>
              ))
            )}

            <View style={{ height: 120 }} />
          </ScrollView>

          {/* Fixed Input Section */}
          {!replyState.commentId && (
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 20}
            >
              <View style={styles.inputSection}>
                <View style={styles.inputRow}>
                  <ProfileImage
                    avatarUrl={userProfile?.avatar_url}
                    displayName={userProfile?.display_name}
                    fullName={userProfile?.full_name}
                    size={28}
                    style={styles.inputAvatar}
                  />
                  <View style={styles.inputWrapper}>
                    <TextInput
                      value={newComment}
                      onChangeText={setNewComment}
                      placeholder="Share words of encouragement..."
                      placeholderTextColor={tokens.colors.text.tertiary}
                      style={styles.textInput}
                      multiline={newComment.length > 50}
                      maxLength={500}
                      editable={!submitting}
                    />
                    <TouchableOpacity
                      style={[
                        styles.sendButton,
                        (!newComment.trim() || submitting) && styles.sendButtonDisabled,
                      ]}
                      onPress={() => handleSubmitComment()}
                      disabled={!newComment.trim() || submitting}
                    >
                      {submitting ? (
                        <ActivityIndicator size="small" color="white" />
                      ) : (
                        <Ionicons name="send" size={14} color="white" />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>

                {newComment.length > 0 && (
                  <Text style={styles.characterCount}>{newComment.length}/500</Text>
                )}
              </View>
            </KeyboardAvoidingView>
          )}
        </View>
      </Modal>

      <ReportModal
        isOpen={showReportModal}
        onClose={() => {
          setShowReportModal(false);
          setReportingCommentId(null);
        }}
        onSubmit={handleSubmitReport}
      />
    </>
  );
}

const styles = createStyles((tokens) => ({
  // Modal Container
  modalContainer: {
    flex: 1,
    backgroundColor: 'black',
  },

  // Gradient Background
  gradientBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(139, 92, 246, 0.03)',
  },

  // Header
  header: {
    paddingTop: tokens.spacing.sm,
    paddingBottom: tokens.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.surface.glassLight, // Enhanced from rgba(255, 255, 255, 0.05) to 0.12
  },
  headerHandle: {
    width: 32,
    height: 3,
    backgroundColor: tokens.colors.surface.glassMedium, // Enhanced from rgba(255, 255, 255, 0.3) to 0.25
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: tokens.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: tokens.spacing.md,
  },
  headerTitle: {
    ...tokens.typography.heading.h3,
    color: tokens.colors.text.primary,
    fontWeight: '500',
  },
  closeButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: surfaces.glass.light,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Prayer Info (Facebook-style)
  prayerInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12, // Facebook uses 12px gap
    paddingHorizontal: 16, // Facebook uses 16px horizontal padding
    paddingVertical: 16, // Facebook uses 16px vertical padding
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  prayerAvatar: {
    borderRadius: tokens.radius.sm,
  },
  prayerInfoText: {
    flex: 1,
  },
  prayerTitle: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.primary,
    fontWeight: '600',
    fontSize: 15, // Facebook post title size
    lineHeight: 20,
    marginBottom: 4, // Small gap like Facebook
  },
  prayerHost: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
    fontSize: 13, // Facebook subtitle size
  },

  // Comments List
  commentsList: {
    flex: 1,
  },
  commentsContent: {
    paddingHorizontal: 16, // Facebook uses 16px horizontal padding
    paddingTop: 8, // Small top padding
  },

  // Loading & Empty States
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing.xl,
    gap: tokens.spacing.xs,
  },
  loadingText: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.tertiary,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing['2xl'],
    gap: tokens.spacing.sm,
  },
  emptyIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: surfaces.glass.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: tokens.spacing.xs,
  },
  emptyText: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.secondary,
  },
  emptySubtext: {
    ...tokens.typography.body.small,
    color: tokens.colors.text.tertiary,
    textAlign: 'center',
  },

  // Comment Items (Perfect Facebook spacing)
  commentContainer: {
    paddingVertical: 12, // Facebook uses 12px vertical spacing between comments
  },
  replyContainer: {
    marginLeft: 44, // Facebook uses exactly 44px for reply indent (32px avatar + 12px gap)
    paddingTop: 8,
    borderLeftWidth: 0, // Facebook doesn't use left borders for replies
    paddingLeft: 0,
  },
  commentRow: {
    flexDirection: 'row',
    gap: 12, // Facebook uses 12px gap between avatar and content
  },
  commentAvatar: {
    borderRadius: tokens.radius.sm,
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8, // Facebook uses 8px between name and timestamp
    marginBottom: 4, // 4px gap before comment text
  },
  authorName: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.primary,
    fontWeight: '600',
    fontSize: 13, // Facebook actually uses 13px for names
  },
  timestamp: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.tertiary,
    fontSize: 12, // Facebook uses 12px for timestamps
  },
  commentText: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.secondary,
    fontSize: 14, // Facebook comment text is 14px
    lineHeight: 20, // Facebook uses 20px line height
    marginBottom: 8, // 8px space before actions
  },
  commentActions: {
    flexDirection: 'row',
    gap: 20, // Facebook uses 20px gap between actions
    paddingTop: 0,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 6, // Facebook uses 6px vertical padding for better touch
    paddingHorizontal: 0,
  },
  actionText: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.quaternary,
    fontSize: 12, // Facebook action text is 12px
    fontWeight: '500', // Facebook uses medium weight, not bold
  },
  actionTextLiked: {
    color: tokens.colors.prayer[400],
  },
  showRepliesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: 8, // Facebook uses 8px for better touch area
    marginTop: 4,
  },
  showRepliesText: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.quaternary,
    fontSize: 12,
    fontWeight: '500', // Facebook uses medium weight
  },
  repliesContainer: {
    marginTop: 8, // 8px like Facebook
  },

  // Reply Input (Facebook-style)
  replyInputContainer: {
    marginTop: 12, // Facebook uses 12px spacing
    marginLeft: 44, // Same as reply indent (32px avatar + 12px gap)
    paddingLeft: 0,
    borderLeftWidth: 0, // Facebook doesn't use left borders
  },
  replyInputRow: {
    flexDirection: 'row',
    gap: 12, // Facebook uses 12px gap
  },
  replyAvatar: {
    borderRadius: tokens.radius.sm,
  },
  replyInputWrapper: {
    flex: 1,
  },
  replyTextInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20, // Facebook uses more rounded corners
    paddingHorizontal: 16, // Facebook uses 16px horizontal padding
    paddingVertical: 10, // Facebook uses 10px vertical padding
    ...tokens.typography.body.medium,
    color: tokens.colors.text.primary,
    fontSize: 14, // Facebook input text size
    minHeight: 36, // Facebook input height
    marginBottom: 8, // 8px space before actions
  },
  replyActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12, // Facebook uses 12px gap
  },
  cancelReplyButton: {
    paddingHorizontal: 12, // Facebook uses 12px padding
    paddingVertical: 8, // Facebook uses 8px padding
    borderRadius: 6,
  },
  cancelReplyText: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.quaternary,
    fontSize: 12,
    fontWeight: '500',
  },
  replySubmitButton: {
    paddingHorizontal: 12, // Facebook uses 12px padding
    paddingVertical: 8, // Facebook uses 8px padding
    borderRadius: 6,
    backgroundColor: tokens.colors.prayer[500],
  },
  replySubmitButtonDisabled: {
    opacity: 0.5,
  },

  // Input Section (Facebook-style)
  inputSection: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.05)',
    paddingHorizontal: 16, // Facebook uses 16px horizontal padding
    paddingVertical: 16, // Facebook uses 16px vertical padding
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12, // Facebook uses 12px gap
  },
  inputAvatar: {
    borderRadius: tokens.radius.sm,
  },
  inputWrapper: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20, // Facebook uses more rounded corners
    paddingHorizontal: 16, // Facebook uses 16px horizontal padding
    paddingVertical: 10, // Facebook uses 10px vertical padding
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12, // Facebook uses 12px gap
    minHeight: 40, // Facebook input height
  },
  textInput: {
    flex: 1,
    ...tokens.typography.body.medium,
    color: tokens.colors.text.primary,
    fontSize: 14, // Facebook input text size
    minHeight: 20,
    maxHeight: 100,
  },
  sendButton: {
    width: 32, // Facebook uses slightly larger buttons
    height: 32,
    borderRadius: 16,
    backgroundColor: tokens.colors.prayer[500],
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.4,
  },
  characterCount: {
    ...tokens.typography.label.small,
    color: tokens.colors.text.quaternary,
    fontSize: 11, // Small like Facebook
    textAlign: 'right',
    marginTop: 8, // Facebook uses 8px space
    paddingRight: 4,
  },

  // Report Modal
  reportModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  reportModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: tokens.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: tokens.colors.surface.glassBorder,
  },
  reportModalTitle: {
    ...tokens.typography.heading.h3,
    color: tokens.colors.text.primary,
  },
  reportCloseButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: tokens.colors.surface.glassMedium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportModalContent: {
    flex: 1,
    padding: tokens.spacing.lg,
  },
  reportModalSubtitle: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.secondary,
    marginBottom: tokens.spacing.lg,
  },
  reasonOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: tokens.spacing.md,
    backgroundColor: tokens.colors.surface.glassLight,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    borderRadius: tokens.radius.lg,
    marginBottom: tokens.spacing.sm,
  },
  reasonOptionSelected: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    borderColor: tokens.colors.success[500],
  },
  reasonText: {
    ...tokens.typography.body.medium,
    color: tokens.colors.text.secondary,
  },
  reasonTextSelected: {
    color: tokens.colors.text.primary,
  },
  descriptionLabel: {
    ...tokens.typography.label.medium,
    color: tokens.colors.text.secondary,
    marginTop: tokens.spacing.lg,
    marginBottom: tokens.spacing.sm,
  },
  descriptionInput: {
    backgroundColor: tokens.colors.surface.glassMedium,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    borderRadius: tokens.radius.lg,
    padding: tokens.spacing.md,
    ...tokens.typography.body.medium,
    color: tokens.colors.text.primary,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  reportModalActions: {
    flexDirection: 'row',
    gap: tokens.spacing.md,
    padding: tokens.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: tokens.colors.surface.glassBorder,
  },
  cancelButton: {
    flex: 1,
    padding: tokens.spacing.md,
    backgroundColor: tokens.colors.surface.glassMedium,
    borderWidth: 1,
    borderColor: tokens.colors.surface.glassBorder,
    borderRadius: tokens.radius.lg,
    alignItems: 'center',
  },
  cancelButtonText: {
    ...tokens.typography.button.medium,
    color: tokens.colors.text.secondary,
  },
  submitButton: {
    flex: 1,
    padding: tokens.spacing.md,
    backgroundColor: tokens.colors.error[500],
    borderRadius: tokens.radius.lg,
    alignItems: 'center',
  },
  submitButtonText: {
    ...tokens.typography.button.medium,
    color: 'white',
  },
}));
