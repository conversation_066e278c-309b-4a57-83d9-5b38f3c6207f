import React from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export default function ShareModal({ isOpen, onClose }: Props) {
  return (
    <Modal visible={isOpen} transparent animationType="fade" onRequestClose={onClose}>
      <View
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.6)',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 24,
        }}
      >
        <View
          style={{
            width: '100%',
            maxWidth: 420,
            backgroundColor: 'rgba(255,255,255,0.08)',
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 1,
            borderRadius: 16,
            padding: 20,
          }}
        >
          <Text style={{ color: 'white', fontSize: 18, marginBottom: 8 }}>Share</Text>
          <Text style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 16 }}>
            Share this prayer with your community.
          </Text>
          <TouchableOpacity
            onPress={onClose}
            style={{
              paddingVertical: 12,
              alignItems: 'center',
              backgroundColor: 'white',
              borderRadius: 10,
            }}
          >
            <Text style={{ color: 'black' }}>Done</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}
