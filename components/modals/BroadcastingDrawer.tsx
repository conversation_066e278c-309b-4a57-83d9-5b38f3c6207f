import React, { useState, useEffect, useCallback } from 'react';
import { Modal, View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { BlurView } from 'expo-blur';
import Ionicons from '@expo/vector-icons/Ionicons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { LiveKitRoom, AudioSession } from '@livekit/react-native';
import LiveKitStateBridge from '../broadcast/LiveKitStateBridge';
import { generateAccessToken } from '../../lib/livekit';
import { useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../lib/supabase';
import Constants from 'expo-constants';

// Detect if we're in Expo Go (where LiveKit won't work)
const isExpoGo = Constants.appOwnership === 'expo';

const { height } = Dimensions.get('window');

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  prayerId?: string;
};

export default function BroadcastingDrawer({
  isOpen,
  onClose,
  title = 'Live Broadcast',
  prayerId,
}: Props) {
  const { user } = useAuth();
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [token, setToken] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [participantCount, setParticipantCount] = useState(0);
  const [isCheckingLiveStatus, setIsCheckingLiveStatus] = useState(false);
  const translateY = useSharedValue(height);
  const opacity = useSharedValue(0);
  const pulseScale = useSharedValue(1);

  // LiveKit server URL from environment
  const serverUrl = process.env.EXPO_PUBLIC_LIVEKIT_URL;

  // Check prayer live status and sync drawer state
  const checkPrayerLiveStatus = useCallback(async () => {
    if (!prayerId) return;

    setIsCheckingLiveStatus(true);
    try {
      const { data: prayer, error } = await supabase
        .from('prayers')
        .select('is_live')
        .eq('id', prayerId)
        .single();

      if (error) {
        console.error('Error checking prayer live status:', error);
        return;
      }

      if (prayer?.is_live && !isRecording) {
        // Prayer is live in database but drawer thinks it's not - sync the state
        console.log('🔄 Syncing drawer state: Prayer is live in database');
        setIsRecording(true);

        // Generate token for existing live session
        if (user && serverUrl) {
          const roomName = `prayer-broadcast-${prayerId}`;
          const participantName =
            user.id || user.user_metadata?.full_name || user.email || 'anonymous';

          try {
            const accessToken = await generateAccessToken(participantName, roomName, true);
            setToken(accessToken);
            console.log('✅ Reconnected to existing live session');
          } catch (err) {
            console.error('❌ Failed to reconnect to live session:', err);
          }
        }
      } else if (!prayer?.is_live && isRecording) {
        // Prayer is not live in database but drawer thinks it is - sync the state
        console.log('🔄 Syncing drawer state: Prayer is not live in database');
        setIsRecording(false);
        setToken(null);
        setRecordingTime(0);
      }
    } catch (error) {
      console.error('Error checking prayer live status:', error);
    } finally {
      setIsCheckingLiveStatus(false);
    }
  }, [prayerId, isRecording, user, serverUrl]);

  // Fetch participant count
  const fetchParticipantCount = useCallback(async () => {
    if (!prayerId) return;

    try {
      const { count } = await supabase
        .from('prayer_joins')
        .select('*', { count: 'exact', head: true })
        .eq('prayer_id', prayerId);

      setParticipantCount(count || 0);
    } catch (error) {
      console.error('Error fetching participant count:', error);
    }
  }, [prayerId]);

  // Animation for slide up/down
  useEffect(() => {
    if (isOpen) {
      opacity.value = withTiming(1, { duration: 300 });
      translateY.value = withSpring(0, { damping: 20, stiffness: 200 });
      checkPrayerLiveStatus(); // Check live status when drawer opens
      fetchParticipantCount();
    } else {
      opacity.value = withTiming(0, { duration: 200 });
      translateY.value = withTiming(height, { duration: 200 });
    }
  }, [isOpen, checkPrayerLiveStatus, fetchParticipantCount]);

  // Recording timer
  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (isRecording) {
      interval = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      setRecordingTime(0);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  // Pulse animation for recording button
  useEffect(() => {
    if (isRecording) {
      pulseScale.value = withRepeat(
        withSequence(withTiming(1.1, { duration: 800 }), withTiming(1, { duration: 800 })),
        -1,
        false,
      );
    } else {
      pulseScale.value = withTiming(1, { duration: 200 });
    }
  }, [isRecording]);

  // Start audio session
  useEffect(() => {
    if (isExpoGo || !isOpen) return;

    const startAudio = async () => {
      try {
        await AudioSession.startAudioSession();
        console.log('🎙️ Audio session started');
      } catch (err) {
        console.error('🎙️ Audio session failed:', err);
      }
    };

    startAudio();
    return () => {
      if (!isRecording) {
        AudioSession.stopAudioSession();
      }
    };
  }, [isOpen, isRecording]);

  // Start broadcast (audio only)
  const startBroadcast = useCallback(async () => {
    if (!user || !serverUrl || !prayerId) return;

    setIsConnecting(true);
    setError(null);

    try {
      const roomName = `prayer-broadcast-${prayerId}`;
      const participantName = user.id || user.user_metadata?.full_name || user.email || 'anonymous';

      console.log('🎯 Starting audio broadcast:', { roomName, participantName });

      const accessToken = await generateAccessToken(participantName, roomName, true);

      // Set prayer as live in database
      const { error: updateError } = await supabase
        .from('prayers')
        .update({ is_live: true })
        .eq('id', prayerId);

      if (updateError) {
        console.error('Failed to set prayer as live:', updateError);
      } else {
        console.log('✅ Prayer marked as live in database');
      }

      setToken(accessToken);
      setIsRecording(true);
      console.log('✅ Audio broadcast token generated, connecting...');
    } catch (err) {
      console.error('❌ Audio broadcast failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to start audio broadcast');
    } finally {
      setIsConnecting(false);
    }
  }, [user, serverUrl, prayerId]);

  // Stop broadcast
  const stopBroadcast = useCallback(async () => {
    try {
      if (prayerId) {
        // Set prayer as not live in database
        const { error: updateError } = await supabase
          .from('prayers')
          .update({ is_live: false })
          .eq('id', prayerId);

        if (updateError) {
          console.error('Failed to set prayer as not live:', updateError);
        } else {
          console.log('✅ Prayer marked as not live in database');
        }
      }
    } catch (err) {
      console.error('Error updating prayer live status:', err);
    }

    setToken(null);
    setIsRecording(false);
    setRecordingTime(0);
    setError(null);
  }, [prayerId]);

  const toggleRecording = () => {
    if (isExpoGo) {
      // Simulator mode
      setIsRecording(!isRecording);
      return;
    }

    if (isRecording) {
      stopBroadcast();
    } else {
      startBroadcast();
    }
  };

  const animatedBackdropStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const animatedSheetStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const animatedPulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: pulseScale.value }],
  }));

  const handleClose = () => {
    opacity.value = withTiming(0, { duration: 200 });
    translateY.value = withTiming(height, { duration: 200 }, () => {
      runOnJS(onClose)();
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isOpen) return null;

  const renderMiniPlayerContent = () => (
    <>
      {/* Mini Player Content */}
      <View style={styles.playerContent}>
        {/* Left: Live indicator and info */}
        <View style={styles.leftSection}>
          {isRecording && (
            <View style={styles.liveIndicator}>
              <Animated.View style={[styles.liveDot, animatedPulseStyle]} />
              <Text style={styles.liveText}>PRAYER LIVE</Text>
            </View>
          )}
          <Text style={styles.playerTitle}>{title}</Text>
          {isRecording && <Text style={styles.recordingTime}>{formatTime(recordingTime)}</Text>}
        </View>

        {/* Center: Main control button */}
        <View style={styles.centerSection}>
          <TouchableOpacity
            onPress={toggleRecording}
            disabled={isConnecting}
            style={[
              styles.mainButton,
              isRecording ? styles.recordingButton : styles.startButton,
              isConnecting && styles.disabledButton,
            ]}
          >
            <Animated.View style={animatedPulseStyle}>
              <Ionicons
                name={isConnecting ? 'hourglass' : isRecording ? 'stop-circle' : 'play-circle'}
                size={24}
                color="white"
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Right: Additional controls */}
        <View style={styles.rightSection}>
          <TouchableOpacity style={styles.controlButton}>
            <Ionicons name="people" size={20} color="rgba(255,255,255,0.8)" />
            <Text style={styles.controlButtonText}>{participantCount}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlButton}>
            <Ionicons name="share" size={20} color="rgba(255,255,255,0.8)" />
          </TouchableOpacity>

          <TouchableOpacity onPress={handleClose} style={styles.controlButton}>
            <Ionicons name="close" size={20} color="rgba(255,255,255,0.8)" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress bar (when recording) */}
      {isRecording && (
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View style={[styles.progressFill, { width: '100%' }]} />
          </View>
          <Text style={styles.progressText}>
            Prayer Live • {formatTime(recordingTime)} • {participantCount} listening
          </Text>
        </View>
      )}
    </>
  );

  return (
    <Modal transparent visible={isOpen} onRequestClose={handleClose}>
      {/* Backdrop */}
      <Animated.View style={[styles.backdrop, animatedBackdropStyle]}>
        <TouchableOpacity style={styles.backdropTouch} onPress={handleClose} />
      </Animated.View>

      {/* Mini Player Drawer */}
      <Animated.View style={[styles.miniPlayer, animatedSheetStyle]}>
        <BlurView intensity={80} tint="dark" style={styles.blurContainer}>
          {/* Handle */}
          <View style={styles.handle} />

          {/* Status Messages */}
          {isCheckingLiveStatus && (
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>Checking broadcast status...</Text>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity onPress={() => setError(null)} style={styles.errorButton}>
                <Text style={styles.errorButtonText}>Dismiss</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* LiveKit Room Wrapper for Audio Only */}
          {token && !isExpoGo ? (
            <LiveKitRoom
              serverUrl={serverUrl}
              token={token}
              connect={true}
              audio={true}
              video={false}
              onConnected={() => {
                console.log('✅ Connected to LiveKit audio room');
              }}
              onDisconnected={() => {
                console.log('🔌 Audio broadcaster disconnected');
                stopBroadcast();
              }}
            >
              {/* Bridge LiveKit -> BroadcastContext */}
              <LiveKitStateBridge />
              {renderMiniPlayerContent()}
            </LiveKitRoom>
          ) : (
            renderMiniPlayerContent()
          )}
        </BlurView>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  backdropTouch: {
    flex: 1,
  },
  miniPlayer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    overflow: 'hidden',
  },
  blurContainer: {
    paddingBottom: 40,
  },
  handle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 12,
    marginBottom: 16,
  },
  playerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  leftSection: {
    flex: 1,
    alignItems: 'flex-start',
  },
  liveIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 4,
  },
  liveDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FF3B30',
    marginRight: 6,
  },
  liveText: {
    color: '#FF3B30',
    fontSize: 12,
    fontWeight: 'bold',
  },
  playerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  recordingTime: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    fontFamily: 'monospace',
  },
  centerSection: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  startButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.8)',
    borderWidth: 2,
    borderColor: 'rgba(0, 122, 255, 0.4)',
  },
  recordingButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    borderWidth: 2,
    borderColor: 'rgba(255, 59, 48, 0.4)',
  },
  disabledButton: {
    opacity: 0.6,
  },
  rightSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 8,
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  controlButtonText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  progressContainer: {
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 8,
  },
  progressBar: {
    height: 3,
    backgroundColor: 'rgba(255,255,255,0.2)',
    borderRadius: 1.5,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FF3B30',
    borderRadius: 1.5,
  },
  progressText: {
    color: 'rgba(255,255,255,0.6)',
    fontSize: 12,
    textAlign: 'center',
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  quickAction: {
    alignItems: 'center',
    padding: 8,
  },
  quickInfo: {
    alignItems: 'center',
    padding: 8,
    opacity: 0.8,
  },
  quickActionText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    marginTop: 4,
    fontWeight: '500',
  },
  statusContainer: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 122, 255, 0.2)',
  },
  statusText: {
    color: '#007AFF',
    fontSize: 14,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 59, 48, 0.2)',
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 14,
    marginBottom: 8,
  },
  errorButton: {
    backgroundColor: 'rgba(255, 59, 48, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  errorButtonText: {
    color: '#FF3B30',
    fontSize: 12,
    fontWeight: '600',
  },
});
