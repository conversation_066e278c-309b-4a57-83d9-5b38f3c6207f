import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';
import { useToast } from '../../hooks/useToast';
import Animated, { FadeIn, FadeOut, SlideInDown, SlideOutDown } from 'react-native-reanimated';

export type ReportContentType = 'comment' | 'prayer' | 'user';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmitReport: (reason: string, details?: string) => void;
  contentType: ReportContentType;
  contentAuthor?: string;
}

interface ReportReason {
  id: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
}

const reportReasons: Record<ReportContentType, ReportReason[]> = {
  comment: [
    { id: 'spam', label: 'Spam or repetitive content', icon: 'mail' },
    { id: 'harassment', label: 'Harassment or bullying', icon: 'warning' },
    { id: 'inappropriate', label: 'Inappropriate content', icon: 'ban' },
    { id: 'hate-speech', label: 'Hate speech or discrimination', icon: 'chatbubble-ellipses' },
    { id: 'misinformation', label: 'False or misleading information', icon: 'close-circle' },
    { id: 'off-topic', label: 'Off-topic or irrelevant', icon: 'location' },
    { id: 'other', label: 'Other (please specify)', icon: 'help-circle' },
  ],
  prayer: [
    { id: 'inappropriate', label: 'Inappropriate content', icon: 'ban' },
    { id: 'spam', label: 'Spam or fake prayer', icon: 'mail' },
    { id: 'hate-speech', label: 'Hate speech or discrimination', icon: 'chatbubble-ellipses' },
    { id: 'misinformation', label: 'Misleading information', icon: 'close-circle' },
    { id: 'commercial', label: 'Commercial or promotional', icon: 'cash' },
    { id: 'other', label: 'Other (please specify)', icon: 'help-circle' },
  ],
  user: [
    { id: 'harassment', label: 'Harassment or bullying', icon: 'warning' },
    { id: 'spam', label: 'Spam behavior', icon: 'mail' },
    { id: 'impersonation', label: 'Impersonation or fake account', icon: 'person' },
    { id: 'hate-speech', label: 'Hate speech or discrimination', icon: 'chatbubble-ellipses' },
    { id: 'inappropriate', label: 'Inappropriate behavior', icon: 'ban' },
    { id: 'other', label: 'Other (please specify)', icon: 'help-circle' },
  ],
};

export default function ReportModal({
  isOpen,
  onClose,
  onSubmitReport,
  contentType,
  contentAuthor,
}: ReportModalProps) {
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [additionalDetails, setAdditionalDetails] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const toast = useToast();

  const reasons = reportReasons[contentType];

  const handleSubmit = async () => {
    if (!selectedReason) {
      toast.error({ title: 'Selection Required', message: 'Please select a reason for reporting' });
      return;
    }

    setIsSubmitting(true);

    try {
      // Add a small delay to show loading state
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSubmitReport(selectedReason, additionalDetails);

      toast.success({
        title: 'Report Submitted Successfully',
        message: 'Our moderation team will review this content within 24 hours.',
      });

      // Reset form and close
      setSelectedReason('');
      setAdditionalDetails('');
      onClose();
    } catch (_error) {
      toast.error({
        title: 'Failed to Submit Report',
        message: 'Please try again later.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedReason('');
      setAdditionalDetails('');
      onClose();
    }
  };

  const getTitle = () => {
    switch (contentType) {
      case 'comment':
        return 'Report Comment';
      case 'prayer':
        return 'Report Prayer';
      case 'user':
        return `Report ${contentAuthor || 'User'}`;
      default:
        return 'Report Content';
    }
  };

  const getDescription = () => {
    switch (contentType) {
      case 'comment':
        return 'Help us maintain a supportive prayer community by reporting inappropriate comments.';
      case 'prayer':
        return 'Report prayers that violate our community guidelines.';
      case 'user':
        return 'Report users who are not following our community standards.';
      default:
        return 'Report content that violates our community guidelines.';
    }
  };

  if (!isOpen) return null;

  return (
    <Modal
      visible={isOpen}
      transparent
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent
    >
      <Animated.View
        entering={FadeIn.duration(200)}
        exiting={FadeOut.duration(200)}
        style={styles.backdrop}
      >
        <TouchableOpacity
          style={styles.backdropTouchable}
          onPress={handleClose}
          activeOpacity={1}
        />

        <Animated.View
          entering={SlideInDown.duration(300).springify()}
          exiting={SlideOutDown.duration(200)}
          style={styles.modalContainer}
        >
          <BlurView intensity={90} tint="dark" style={styles.modalContent}>
            <View style={styles.modal}>
              {/* Header */}
              <View style={styles.header}>
                <View style={styles.headerText}>
                  <Text style={styles.title}>{getTitle()}</Text>
                  <Text style={styles.description}>{getDescription()}</Text>
                </View>
                <TouchableOpacity
                  onPress={handleClose}
                  disabled={isSubmitting}
                  style={[styles.closeButton, isSubmitting && styles.disabled]}
                >
                  <Ionicons name="close" size={24} color="rgba(255,255,255,0.6)" />
                </TouchableOpacity>
              </View>

              <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
                {/* Reason Selection */}
                <View style={styles.section}>
                  <Text style={styles.sectionLabel}>Why are you reporting this {contentType}?</Text>
                  <View style={styles.reasonList}>
                    {reasons.map((reason) => (
                      <TouchableOpacity
                        key={reason.id}
                        onPress={() => setSelectedReason(reason.id)}
                        style={[
                          styles.reasonItem,
                          selectedReason === reason.id && styles.reasonItemSelected,
                        ]}
                      >
                        <View style={styles.reasonIcon}>
                          <Ionicons
                            name={reason.icon}
                            size={20}
                            color={selectedReason === reason.id ? '#fff' : 'rgba(255,255,255,0.6)'}
                          />
                        </View>
                        <Text
                          style={[
                            styles.reasonLabel,
                            selectedReason === reason.id && styles.reasonLabelSelected,
                          ]}
                        >
                          {reason.label}
                        </Text>
                        <View
                          style={[
                            styles.radioButton,
                            selectedReason === reason.id && styles.radioButtonSelected,
                          ]}
                        >
                          {selectedReason === reason.id && <View style={styles.radioButtonInner} />}
                        </View>
                      </TouchableOpacity>
                    ))}
                  </View>
                </View>

                {/* Additional Details */}
                <View style={styles.section}>
                  <Text style={styles.sectionLabel}>Additional details (optional)</Text>
                  <TextInput
                    value={additionalDetails}
                    onChangeText={setAdditionalDetails}
                    placeholder="Provide any additional context that might help our review..."
                    placeholderTextColor="rgba(255,255,255,0.4)"
                    maxLength={500}
                    multiline
                    numberOfLines={4}
                    style={styles.textArea}
                    textAlignVertical="top"
                  />
                  <Text style={styles.characterCount}>
                    {additionalDetails.length}/500 characters
                  </Text>
                </View>
              </ScrollView>

              {/* Actions */}
              <View style={styles.actions}>
                <TouchableOpacity
                  onPress={handleClose}
                  disabled={isSubmitting}
                  style={[styles.button, styles.cancelButton, isSubmitting && styles.disabled]}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleSubmit}
                  disabled={!selectedReason || isSubmitting}
                  style={[
                    styles.button,
                    styles.submitButton,
                    (!selectedReason || isSubmitting) && styles.disabled,
                  ]}
                >
                  {isSubmitting ? (
                    <View style={styles.loadingContainer}>
                      <Ionicons name="hourglass" size={16} color="#fff" />
                      <Text style={styles.submitButtonText}>Submitting...</Text>
                    </View>
                  ) : (
                    <Text style={styles.submitButtonText}>Submit Report</Text>
                  )}
                </TouchableOpacity>
              </View>

              {/* Footer Notice */}
              <View style={styles.footer}>
                <Text style={styles.footerText}>
                  Reports are reviewed by our moderation team. False reports may result in account
                  restrictions.
                </Text>
              </View>
            </View>
          </BlurView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backdropTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  modalContent: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  modal: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    borderRadius: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    padding: 24,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  headerText: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    fontFamily: 'Archivo-SemiBold',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.6)',
    fontFamily: 'Roboto-Regular',
    lineHeight: 20,
  },
  closeButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 16,
  },
  content: {
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
    fontFamily: 'Archivo-Medium',
    marginBottom: 16,
  },
  reasonList: {
    gap: 8,
  },
  reasonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
  },
  reasonItemSelected: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderColor: 'rgba(255,255,255,0.3)',
  },
  reasonIcon: {
    marginRight: 12,
  },
  reasonLabel: {
    flex: 1,
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
    fontFamily: 'Roboto-Regular',
  },
  reasonLabelSelected: {
    color: '#fff',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'rgba(255,255,255,0.4)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonSelected: {
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  radioButtonInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#000',
  },
  textArea: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: '#fff',
    fontSize: 14,
    fontFamily: 'Roboto-Regular',
    minHeight: 100,
  },
  characterCount: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.4)',
    fontFamily: 'Roboto-Regular',
    marginTop: 8,
    textAlign: 'right',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
    fontFamily: 'Archivo-Medium',
  },
  submitButton: {
    backgroundColor: 'rgba(239,68,68,0.8)',
    borderWidth: 1,
    borderColor: 'rgba(239,68,68,0.4)',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#fff',
    fontFamily: 'Archivo-Medium',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  footerText: {
    fontSize: 12,
    color: 'rgba(255,255,255,0.5)',
    fontFamily: 'Roboto-Regular',
    lineHeight: 16,
  },
  disabled: {
    opacity: 0.5,
  },
});
