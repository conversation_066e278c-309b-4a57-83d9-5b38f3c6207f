import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { tokens } from '../../lib/design/tokens';

interface ContentContainerProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  paddingHorizontal?: 'none' | 'sm' | 'md' | 'lg';
  paddingVertical?: 'none' | 'sm' | 'md' | 'lg';
}

/**
 * Standardized content container that provides consistent horizontal padding
 * and spacing for screen content. Follows 8px grid system.
 */
export default function ContentContainer({
  children,
  style,
  padding = 'md',
  paddingHorizontal,
  paddingVertical,
}: ContentContainerProps) {
  const paddingStyles = [];

  // Apply padding based on props
  if (padding !== 'none') {
    paddingStyles.push(styles[`padding_${padding}`]);
  }

  if (paddingHorizontal && paddingHorizontal !== 'none') {
    paddingStyles.push(styles[`paddingHorizontal_${paddingHorizontal}`]);
  }

  if (paddingVertical && paddingVertical !== 'none') {
    paddingStyles.push(styles[`paddingVertical_${paddingVertical}`]);
  }

  return <View style={[...paddingStyles, style]}>{children}</View>;
}

const styles = StyleSheet.create({
  // Standard padding options
  padding_sm: {
    padding: tokens.spacing.sm, // 8px
  },
  padding_md: {
    padding: tokens.spacing.md, // 16px - standard content padding
  },
  padding_lg: {
    padding: tokens.spacing.lg, // 24px
  },

  // Horizontal padding options
  paddingHorizontal_sm: {
    paddingHorizontal: tokens.spacing.sm, // 8px
  },
  paddingHorizontal_md: {
    paddingHorizontal: tokens.spacing.md, // 16px - standard horizontal padding
  },
  paddingHorizontal_lg: {
    paddingHorizontal: tokens.spacing.lg, // 24px
  },

  // Vertical padding options
  paddingVertical_sm: {
    paddingVertical: tokens.spacing.sm, // 8px
  },
  paddingVertical_md: {
    paddingVertical: tokens.spacing.md, // 16px
  },
  paddingVertical_lg: {
    paddingVertical: tokens.spacing.lg, // 24px
  },
});
