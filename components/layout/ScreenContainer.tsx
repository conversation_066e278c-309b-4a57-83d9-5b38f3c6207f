import React from 'react';
import { View, ScrollView, StyleSheet, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import GradientBackground from '../GradientBackground';
import { tokens } from '../../lib/design/tokens';

interface ScreenContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: ViewStyle;
  style?: ViewStyle;
  safeArea?: boolean;
  withGradient?: boolean;
}

/**
 * Standardized screen container component that ensures consistent spacing and layout
 * across all screens in the app. Follows 8px grid system and mobile UX best practices.
 */
export default function ScreenContainer({
  children,
  scrollable = true,
  showsVerticalScrollIndicator = false,
  contentContainerStyle,
  style,
  safeArea = false,
  withGradient = true,
}: ScreenContainerProps) {
  const Container = safeArea ? SafeAreaView : View;
  const Content = scrollable ? ScrollView : View;

  const containerStyle = [styles.container, style];

  const contentStyle = scrollable
    ? [styles.scrollContent, contentContainerStyle]
    : [styles.content, contentContainerStyle];

  return (
    <Container style={containerStyle}>
      {withGradient && <GradientBackground />}
      <Content
        style={scrollable ? styles.scrollView : styles.content}
        contentContainerStyle={scrollable ? contentStyle : undefined}
        showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      >
        {children}
        {/* Standard bottom padding for tab bar + safe area */}
        <View style={styles.bottomPadding} />
      </Content>
    </Container>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingTop: tokens.spacing['6xl'], // 96px - consistent header spacing
  },
  scrollContent: {
    paddingTop: tokens.spacing['6xl'], // 96px - consistent header spacing
  },
  bottomPadding: {
    height: tokens.spacing['6xl'], // 96px - consistent bottom spacing for tab bar
  },
});
