import React, { createContext, useContext, useMemo, useState } from 'react';
import Toast from 'react-native-toast-message';

type NotificationType = 'comment' | 'like' | 'prayer_update' | 'follow' | 'broadcast' | 'general';

interface NotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: string;
  userName?: string;
  isRead?: boolean;
  relatedId?: string;
}

interface NotificationContextType {
  notifications: NotificationItem[];
  unreadCount: number;
  add: (n: Omit<NotificationItem, 'id' | 'timestamp'>) => void;
  markAllAsRead: () => void;
}

const Ctx = createContext<NotificationContextType | undefined>(undefined);

export function NotificationsProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const add: NotificationContextType['add'] = (n) => {
    const item: NotificationItem = {
      ...n,
      id: String(Date.now()),
      timestamp: new Date().toISOString(),
      isRead: false,
    };
    setNotifications((prev) => [item, ...prev]);

    // Map notification types to toast types
    const getToastType = (type: NotificationType) => {
      switch (type) {
        case 'prayer_update':
        case 'like':
          return 'prayer';
        case 'broadcast':
          return 'broadcast';
        case 'comment':
        case 'follow':
          return 'info';
        default:
          return 'info';
      }
    };

    // Safe-area aware top offset for toasts
    const insetsTop =
      (typeof globalThis === 'object' && (globalThis as any).__TOAST_TOP_OFFSET__) || 60;
    Toast.show({
      type: getToastType(item.type) as any,
      text1: item.title,
      text2: item.message,
      position: 'top',
      visibilityTime: 4000,
      autoHide: true,
      topOffset: insetsTop,
    });
  };

  const markAllAsRead = () => setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));

  const value = useMemo(
    () => ({ notifications, unreadCount, add, markAllAsRead }),
    [notifications, unreadCount],
  );

  return <Ctx.Provider value={value}>{children}</Ctx.Provider>;
}

export const useNotifications = () => {
  const ctx = useContext(Ctx);
  if (!ctx) throw new Error('useNotifications must be used within NotificationsProvider');
  return ctx;
};
