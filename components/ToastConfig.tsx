import React from 'react';
import { View, Text } from 'react-native';
import Ionicons from '@expo/vector-icons/Ionicons';

// Simple, high-contrast toast components that actually work
const CustomToast = (props: any) => {
  const { text1, text2, iconName, iconColor = '#fff', backgroundColor = '#1a1a1a' } = props;

  return (
    <View
      style={{
        marginHorizontal: 16,
        marginTop: 8,
        borderRadius: 12,
        backgroundColor: backgroundColor,
        borderWidth: 1,
        borderColor: 'rgba(255, 255, 255, 0.2)',
        padding: 16,
        flexDirection: 'row',
        alignItems: 'center',
        minHeight: 60,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
      }}
    >
      <View
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          borderRadius: 8,
          padding: 8,
          marginRight: 12,
          alignItems: 'center',
          justifyContent: 'center',
          width: 32,
          height: 32,
        }}
      >
        <Ionicons name={iconName} size={18} color={iconColor} />
      </View>
      <View style={{ flex: 1 }}>
        {!!text1 && (
          <Text
            style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#ffffff',
              marginBottom: text2 ? 4 : 0,
            }}
            numberOfLines={2}
          >
            {text1}
          </Text>
        )}
        {!!text2 && (
          <Text
            style={{
              fontSize: 14,
              fontWeight: '400',
              color: 'rgba(255, 255, 255, 0.8)',
            }}
            numberOfLines={3}
          >
            {text2}
          </Text>
        )}
      </View>
    </View>
  );
};

// Success Toast
const SuccessToast = (props: any) => {
  return (
    <CustomToast
      {...props}
      iconName="checkmark-circle"
      iconColor="#22c55e"
      backgroundColor="#0f1419"
    />
  );
};

// Error Toast
const ErrorToastCustom = (props: any) => {
  return (
    <CustomToast {...props} iconName="close-circle" iconColor="#ef4444" backgroundColor="#1a0f0f" />
  );
};

// Info Toast
const InfoToastCustom = (props: any) => {
  return (
    <CustomToast
      {...props}
      iconName="information-circle"
      iconColor="#3b82f6"
      backgroundColor="#0f1419"
    />
  );
};

// Warning Toast
const WarningToast = (props: any) => {
  return (
    <CustomToast {...props} iconName="warning" iconColor="#f59e0b" backgroundColor="#1a1610" />
  );
};

// Prayer-specific Toast
const PrayerToast = (props: any) => {
  return <CustomToast {...props} iconName="heart" iconColor="#ec4899" backgroundColor="#1a0f17" />;
};

// Broadcasting Toast
const BroadcastToast = (props: any) => {
  return <CustomToast {...props} iconName="radio" iconColor="#8b5cf6" backgroundColor="#14101a" />;
};

export const toastConfig = {
  success: SuccessToast,
  error: ErrorToastCustom,
  info: InfoToastCustom,
  warning: WarningToast,
  prayer: PrayerToast,
  broadcast: BroadcastToast,
};

// Styles are now handled by the design system in CustomToast component
