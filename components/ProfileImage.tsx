import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, ViewStyle } from 'react-native';

interface ProfileImageProps {
  avatarUrl?: string | null;
  displayName?: string | null;
  fullName?: string | null;
  size?: number;
  style?: ViewStyle;
  textStyle?: any;
}

export default function ProfileImage({
  avatarUrl,
  displayName,
  fullName,
  size = 40,
  style,
  textStyle,
}: ProfileImageProps) {
  const [imageError, setImageError] = useState(false);

  // Get initials from name
  const getInitials = () => {
    const name = displayName || fullName || '';
    return (
      name
        .split(' ')
        .map((word) => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2) || '?'
    );
  };

  const containerStyle = [
    styles.container,
    {
      width: size,
      height: size,
      borderRadius: size / 2,
    },
    style,
  ];

  const fontSize = size * 0.4; // Font size relative to container size

  // Show image if available and not errored
  if (avatarUrl && !imageError) {
    return (
      <View style={containerStyle}>
        <Image
          source={{ uri: avatarUrl }}
          style={[styles.image, { width: size, height: size, borderRadius: size / 2 }]}
          onError={() => setImageError(true)}
        />
      </View>
    );
  }

  // Fall back to initials
  return (
    <View style={containerStyle}>
      <Text style={[styles.initials, { fontSize }, textStyle]}>{getInitials()}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(255,255,255,0.12)',
    borderWidth: 1.5,
    borderColor: 'rgba(255,255,255,0.25)',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden', // Prevents border corner issues
  },
  image: {
    resizeMode: 'cover',
    borderWidth: 1.5,
    borderColor: 'rgba(255,255,255,0.25)',
  },
  initials: {
    fontWeight: '600',
    color: 'white',
    letterSpacing: 0.5,
  },
});
