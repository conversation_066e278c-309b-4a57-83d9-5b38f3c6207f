import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Animated, Text } from 'react-native';
import GradientBackground from './GradientBackground';
import VideoLoader from './VideoLoader';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 16,
    fontFamily: 'Roboto-Regular',
    marginTop: 16,
    textAlign: 'center',
  },
});

interface VideoLoadingScreenProps {
  loading?: boolean;
  showText?: boolean;
  loadingText?: string;
  onFinish?: () => void; // called when fade-out completes
  fadeOutDelay?: number; // delay before starting fade-out (in ms)
  fadeOutDuration?: number; // duration of fade-out animation (in ms)
}

export default function VideoLoadingScreen({
  loading = true,
  showText = false,
  loadingText = 'Loading...',
  onFinish,
  fadeOutDelay = 2000, // 2 seconds delay to let video play longer
  fadeOutDuration = 1500, // 1.5 seconds fade-out
}: VideoLoadingScreenProps) {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const contentFadeAnim = useRef(new Animated.Value(1)).current;
  const [visible, setVisible] = useState(true);

  const fadeOutTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const finishingRef = useRef(false);

  // Start overlay fade-in when component mounts
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  // When loading finishes → start fade-out sequence with delay
  useEffect(() => {
    if (!loading && !finishingRef.current) {
      finishingRef.current = true;

      // Clear any existing timeout
      if (fadeOutTimeoutRef.current) {
        clearTimeout(fadeOutTimeoutRef.current);
      }

      // Wait for the delay, then start fade-out
      fadeOutTimeoutRef.current = setTimeout(() => {
        // First fade out the content (text/loader)
        Animated.timing(contentFadeAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }).start(() => {
          // Then fade out the video background
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: fadeOutDuration,
            useNativeDriver: true,
          }).start(() => {
            onFinish?.();
            setVisible(false);
          });
        });
      }, fadeOutDelay);
    }
  }, [loading, fadeOutDelay, fadeOutDuration]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fadeOutTimeoutRef.current) {
        clearTimeout(fadeOutTimeoutRef.current);
      }
    };
  }, []);

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* Background image/gradient only (no video) */}
      <GradientBackground />

      {/* Loading overlay */}
      <Animated.View style={[styles.contentOverlay, { opacity: fadeAnim }]}>
        {showText && (
          <Animated.View style={{ opacity: contentFadeAnim }}>
            <View style={styles.loadingContent}>
              <VideoLoader size="large" color="rgba(255, 255, 255, 0.8)" />
              <Text style={styles.loadingText}>{loadingText}</Text>
            </View>
          </Animated.View>
        )}
      </Animated.View>
    </View>
  );
}
