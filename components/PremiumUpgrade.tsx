import React from 'react';
import { Modal, View, Text, TouchableOpacity } from 'react-native';
import { UpgradeReason } from '../hooks/usePremium';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  trigger: UpgradeReason;
};

export default function PremiumUpgrade({ isOpen, onClose, onUpgrade, trigger }: Props) {
  return (
    <Modal visible={isOpen} transparent animationType="fade" onRequestClose={onClose}>
      <View
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.6)',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 24,
        }}
      >
        <View
          style={{
            width: '100%',
            maxWidth: 420,
            backgroundColor: 'rgba(255,255,255,0.08)',
            borderColor: 'rgba(255,255,255,0.2)',
            borderWidth: 1,
            borderRadius: 16,
            padding: 20,
          }}
        >
          <Text style={{ color: 'white', fontSize: 20, marginBottom: 8 }}>Go Premium</Text>
          <Text style={{ color: 'rgba(255,255,255,0.8)', marginBottom: 16 }}>
            Unlock broadcasting, unlimited prayers, and more. (Triggered: {trigger})
          </Text>
          <View style={{ flexDirection: 'row', gap: 8 }}>
            <TouchableOpacity
              onPress={onClose}
              style={{
                flex: 1,
                paddingVertical: 12,
                alignItems: 'center',
                borderColor: 'rgba(255,255,255,0.3)',
                borderWidth: 1,
                borderRadius: 10,
              }}
            >
              <Text style={{ color: 'white' }}>Not now</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onUpgrade}
              style={{
                flex: 1,
                paddingVertical: 12,
                alignItems: 'center',
                backgroundColor: 'white',
                borderRadius: 10,
              }}
            >
              <Text style={{ color: 'black' }}>Upgrade</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}
