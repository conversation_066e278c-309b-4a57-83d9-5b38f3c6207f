import { useEffect, useMemo, useRef } from 'react';
import { useBroadcast } from '../../contexts/BroadcastContext';
import { BroadcastParticipant } from '../../lib/livekit';
import { useParticipants, useLocalParticipant } from '@livekit/react-native';

/**
 * LiveKitStateBridge
 * Inside a <LiveKitRoom>, bridges LiveKit room/participants state into BroadcastContext
 * without rendering any UI.
 */
export default function LiveKitStateBridge() {
  const { setParticipants, setConnected } = useBroadcast();
  const lkParticipants = useParticipants();

  // Track first-seen times for participants to approximate joinedAt
  const joinTimesRef = useRef<Map<string, Date>>(new Map());

  // We cannot read connection state without useRoom; instead infer from participant list
  useEffect(() => {
    setConnected(lkParticipants.length > 0);
  }, [lkParticipants.length]);
  const { localParticipant } = useLocalParticipant();

  // Map LiveKit participants to app participants
  const { isMicMuted: ctxMuted } = useBroadcast();

  // Determine precise mute state from LiveKit track publications
  function computeMuted(p: any): boolean {
    // Local participant: prefer SDK microphone flag if present, else fall back to context
    if (p?.isLocal) {
      const micEnabled = (p as any)?.isMicrophoneEnabled;
      if (typeof micEnabled === 'boolean') {
        return !micEnabled;
      }
      // Fallback to context toggle when SDK flag not available
      return ctxMuted;
      // In this app, only the host publishes audio. Treat non-hosts as permanently muted/listeners.
    }

    // Remote participants: inspect audio publications
    const pubs: any[] = [];
    // Common in JS SDK
    if (p?.audioTracks && typeof p.audioTracks.forEach === 'function') {
      p.audioTracks.forEach((pub: any) => pubs.push(pub));
    }
    // Some builds expose trackPublications map with kind/source
    if (p?.trackPublications && typeof p.trackPublications.forEach === 'function') {
      p.trackPublications.forEach((pub: any) => {
        const kind = (pub?.track?.kind || pub?.kind)?.toString?.().toLowerCase?.();
        const source = (pub?.source || '').toString().toLowerCase();
        if (kind?.includes('audio') || source === 'microphone') pubs.push(pub);
      });
    }

    if (pubs.length === 0) {
      // No audio publications visible yet: treat as muted (listeners)
      return true;
    }

    // If every audio publication reports muted, consider participant muted
    return pubs.every((pub: any) => {
      if (typeof pub?.isMuted === 'boolean') return pub.isMuted;
      // If track object exists, some SDKs expose muted on track
      if (typeof pub?.track?.isMuted === 'boolean') return pub.track.isMuted;
      // If none of the above, be conservative and consider muted
      return true;
    });
  }

  const mappedParticipants: BroadcastParticipant[] = useMemo(() => {
    return lkParticipants.map((p) => {
      const id = (p as any).identity ?? (p as any).sid ?? Math.random().toString(36).slice(2);
      if (!joinTimesRef.current.has(id)) {
        joinTimesRef.current.set(id, new Date());
      }
      const isHost = (p as any).isLocal === true;
      const isMuted = computeMuted(p);
      return {
        id,
        name: (p as any).name || 'Guest',
        isMuted,
        isHost,
        joinedAt: joinTimesRef.current.get(id)!,
      } as BroadcastParticipant;
    });
  }, [lkParticipants, ctxMuted]);

  // Reflect context mute toggle to LiveKit local microphone when available
  useEffect(() => {
    if (!localParticipant || typeof localParticipant.setMicrophoneEnabled !== 'function') return;
    // LiveKit expects true to enable microphone; ctxMuted true means we want it disabled
    localParticipant.setMicrophoneEnabled(!ctxMuted).catch(() => {});
  }, [localParticipant, ctxMuted]);

  // Push into context
  useEffect(() => {
    setParticipants(mappedParticipants);
  }, [mappedParticipants]);

  // Optionally, reflect local mic state to context in the future
  // Example:
  // useEffect(() => {
  //   if (!localParticipant) return;
  //   // setMuted(!localParticipant.isMicrophoneEnabled)
  // }, [localParticipant?.isMicrophoneEnabled])

  return null;
}
