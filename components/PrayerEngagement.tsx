import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { BlurView } from 'expo-blur';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../hooks/useToast';
import { supabase, PrayerWithProfile } from '../lib/supabase';
import { blur } from '../lib/design';

interface PrayerEngagementProps {
  prayer: PrayerWithProfile;
  onListenLive?: () => void;
  onStartBroadcast?: () => void;
}

// Unified Prayer Engagement Button - handles all prayer states and user types
const UnifiedPrayerButton: React.FC<{
  prayer: PrayerWithProfile;
  onStartBroadcast?: () => void;
  onListenLive?: () => void;
}> = ({ prayer, onStartBroadcast, onListenLive }) => {
  const { user } = useAuth();
  const toast = useToast();
  const [isJoined, setIsJoined] = useState(false);
  const [joinCount, setJoinCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Check if current user is the prayer creator
  const isCreator = user?.id === prayer.user_id;

  // Fetch join status and count
  useEffect(() => {
    const fetchJoinStatus = async () => {
      if (!user) return;

      try {
        // Check if user has joined this prayer
        const { data: joinData } = await supabase
          .from('prayer_joins')
          .select('id')
          .eq('user_id', user.id)
          .eq('prayer_id', prayer.id)
          .single();

        setIsJoined(!!joinData);

        // Get total join count
        const { count } = await supabase
          .from('prayer_joins')
          .select('*', { count: 'exact', head: true })
          .eq('prayer_id', prayer.id);

        setJoinCount(count || 0);
      } catch (_error) {
        console.error('Error fetching join status:', _error);
      }
    };

    fetchJoinStatus();
  }, [user, prayer.id]);

  // Determine button state and action
  const getButtonState = () => {
    // Prayer is answered - show completed state
    if (prayer.is_answered) {
      return {
        icon: 'checkmark-circle',
        text: 'Prayer Answered',
        color: '#86efac',
        backgroundColor: 'rgba(134, 239, 172, 0.1)',
        borderColor: 'rgba(134, 239, 172, 0.3)',
        disabled: true,
        showCount: !isCreator,
      };
    }

    // Creator states
    if (isCreator) {
      if (prayer.is_live) {
        // Creator's prayer is currently live - allow them to manage the broadcast
        return {
          icon: 'radio',
          text: 'Manage Live',
          color: '#FF6B6B',
          backgroundColor: 'rgba(255, 107, 107, 0.1)',
          borderColor: 'rgba(255, 107, 107, 0.3)',
          disabled: false,
          action: 'manage_broadcast',
          showCount: false,
        };
      } else if (prayer.allows_broadcast) {
        // Creator can start broadcast
        return {
          icon: 'play-circle',
          text: 'Start Live Prayer',
          color: '#007AFF',
          backgroundColor: 'rgba(0, 122, 255, 0.1)',
          borderColor: 'rgba(0, 122, 255, 0.3)',
          disabled: false,
          action: 'start_broadcast',
          showCount: false,
        };
      } else {
        // Creator's timeless prayer
        return {
          icon: 'heart',
          text: 'Your Prayer',
          color: '#4ECDC4',
          backgroundColor: 'rgba(78, 205, 196, 0.1)',
          borderColor: 'rgba(78, 205, 196, 0.3)',
          disabled: true,
          showCount: false,
        };
      }
    }

    // Non-creator states
    if (prayer.is_live && prayer.allows_broadcast) {
      // Prayer is currently live - user can join broadcast
      return {
        icon: 'headset',
        text: 'Join Broadcast',
        color: '#FF6B6B',
        backgroundColor: 'rgba(255, 107, 107, 0.1)',
        borderColor: 'rgba(255, 107, 107, 0.3)',
        disabled: false,
        action: 'join_broadcast',
        showCount: true,
      };
    } else if (prayer.allows_broadcast) {
      // Broadcast prayer but not live yet - remind me
      return {
        icon: 'bell',
        text: 'Remind Me',
        color: '#FFA500',
        backgroundColor: 'rgba(255, 165, 0, 0.1)',
        borderColor: 'rgba(255, 165, 0, 0.3)',
        disabled: false,
        action: 'remind_me',
        showCount: true,
      };
    } else {
      // Timeless prayer - join in prayer
      return {
        icon: isJoined ? 'heart' : 'heart-outline',
        text: isJoined ? 'Praying' : 'Join in Prayer',
        color: isJoined ? '#4ECDC4' : 'rgba(255,255,255,0.8)',
        backgroundColor: isJoined ? 'rgba(78, 205, 196, 0.1)' : 'rgba(255, 255, 255, 0.1)',
        borderColor: isJoined ? 'rgba(78, 205, 196, 0.3)' : 'rgba(255, 255, 255, 0.2)',
        disabled: false,
        action: 'toggle_join',
        showCount: true,
      };
    }
  };

  const handleButtonPress = async () => {
    if (!user || isLoading) return;

    const buttonState = getButtonState();
    if (buttonState.disabled) return;

    switch (buttonState.action) {
      case 'start_broadcast':
        onStartBroadcast?.();
        break;

      case 'manage_broadcast':
        onStartBroadcast?.(); // This will open the BroadcastingDrawer for management
        break;

      case 'join_broadcast':
        onListenLive?.();
        break;

      case 'remind_me':
        // TODO: Implement reminder functionality
        toast.showSuccessToast('Reminder Set', "We'll notify you when this prayer goes live");
        break;

      case 'toggle_join':
        await handleToggleJoin();
        break;
    }
  };

  const handleToggleJoin = async () => {
    setIsLoading(true);
    const newIsJoined = !isJoined;
    const newJoinCount = newIsJoined ? joinCount + 1 : Math.max(joinCount - 1, 0);

    // Optimistic update
    setIsJoined(newIsJoined);
    setJoinCount(newJoinCount);

    try {
      if (newIsJoined) {
        // Join prayer
        const { error } = await supabase.from('prayer_joins').upsert(
          {
            user_id: user!.id,
            prayer_id: prayer.id,
          },
          {
            onConflict: 'user_id,prayer_id',
          },
        );

        if (error) {
          // Revert on error
          setIsJoined(false);
          setJoinCount(joinCount);
          toast.showErrorToast('Error', 'Failed to join prayer');
        } else {
          toast.showSuccessToast('Joined Prayer', 'You are now praying with others');
        }
      } else {
        // Leave prayer
        const { error } = await supabase
          .from('prayer_joins')
          .delete()
          .eq('user_id', user!.id)
          .eq('prayer_id', prayer.id);

        if (error) {
          // Revert on error
          setIsJoined(true);
          setJoinCount(joinCount);
          toast.showErrorToast('Error', 'Failed to leave prayer');
        } else {
          toast.showSuccessToast('Left Prayer', 'You are no longer joined');
        }
      }
    } catch (_error) {
      // Revert on error
      setIsJoined(!newIsJoined);
      setJoinCount(joinCount);
      toast.showErrorToast('Error', 'Something went wrong');
    } finally {
      setIsLoading(false);
    }
  };

  const buttonState = getButtonState();

  return (
    <BlurView intensity={blur.maximum} style={styles.buttonBlurContainer}>
      <TouchableOpacity
        onPress={handleButtonPress}
        style={[
          styles.engagementButton,
          {
            backgroundColor: buttonState.backgroundColor,
            borderColor: buttonState.borderColor,
          },
        ]}
        disabled={buttonState.disabled || isLoading}
      >
        <Ionicons name={buttonState.icon as any} size={20} color={buttonState.color} />
        <Text style={[styles.engagementText, { color: buttonState.color }]}>{buttonState.text}</Text>
        {buttonState.showCount && (
          <Text
            style={[
              styles.countText,
              {
                color: buttonState.color,
                backgroundColor: buttonState.backgroundColor,
              },
            ]}
          >
            {joinCount}
          </Text>
        )}
      </TouchableOpacity>
    </BlurView>
  );
};

// Combined participant list
const ParticipantsList: React.FC<{ prayer: PrayerWithProfile }> = ({ prayer }) => {
  const [prayerSupporters, setPrayerSupporters] = useState<any[]>([]);
  const [liveListeners, _setLiveListeners] = useState([]);

  useEffect(() => {
    const fetchParticipants = async () => {
      try {
        // Get prayer supporters (joined prayer)
        const { data: supporters } = await supabase
          .from('prayer_joins')
          .select(
            `
            user_id,
            created_at,
            user_profiles (
              display_name,
              full_name,
              avatar_url
            )
          `,
          )
          .eq('prayer_id', prayer.id)
          .order('created_at', { ascending: false })
          .limit(10);

        setPrayerSupporters(supporters || []);

        // TODO: Get live listeners from LiveKit room info
        // This would require LiveKit room metadata or participant tracking
      } catch (_error) {
        console.error('Error fetching participants:', _error);
      }
    };

    fetchParticipants();
  }, [prayer.id]);

  return (
    <View style={styles.participantsContainer}>
      {/* Prayer Supporters */}
      {prayerSupporters.length > 0 && (
        <View style={styles.participantSection}>
          <Text style={styles.sectionTitle}>📿 Praying Together ({prayerSupporters.length})</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.participantsList}
          >
            {prayerSupporters.map((supporter: any, index) => (
              <View key={supporter.user_id || index} style={styles.participantItem}>
                <View style={styles.participantAvatar}>
                  <Text style={styles.participantInitial}>
                    {(supporter.user_profiles?.display_name ||
                      supporter.user_profiles?.full_name ||
                      'A')[0].toUpperCase()}
                  </Text>
                </View>
                <Text style={styles.participantName}>
                  {supporter.user_profiles?.display_name ||
                    supporter.user_profiles?.full_name ||
                    'Anonymous'}
                </Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Live Listeners */}
      {prayer.is_live && liveListeners.length > 0 && (
        <View style={styles.participantSection}>
          <Text style={styles.sectionTitle}>🎙️ Listening Live ({liveListeners.length})</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.participantsList}
          >
            {liveListeners.map((listener: any, index) => (
              <View key={listener.id || index} style={styles.participantItem}>
                <View style={[styles.participantAvatar, styles.liveAvatar]}>
                  <Text style={styles.participantInitial}>
                    {(listener.name || 'A')[0].toUpperCase()}
                  </Text>
                </View>
                <Text style={styles.participantName}>{listener.name || 'Anonymous'}</Text>
              </View>
            ))}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

// Main engagement component
export const PrayerEngagement: React.FC<PrayerEngagementProps> = ({
  prayer,
  onListenLive,
  onStartBroadcast,
}) => {
  return (
    <View style={styles.container}>
      {/* Single Unified Engagement Button - Standalone */}
      <View style={styles.engagementRow}>
        <UnifiedPrayerButton
          prayer={prayer}
          onStartBroadcast={onStartBroadcast}
          onListenLive={onListenLive}
        />
      </View>

      {/* Participants List - Hidden for standalone button appearance */}
      {/* <ParticipantsList prayer={prayer} /> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: 'transparent', // Ensure no background interference
  },
  engagementRow: {
    flexDirection: 'row',
    gap: 12,
    // Remove marginBottom since we're hiding participants list
  },
  buttonBlurContainer: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    // Strong shadow for prominence
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
    // Subtle border for definition
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  engagementButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 20,
    gap: 12,
    // Remove individual button styling since blur is handled by container
  },

  engagementText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '600',
  },

  countText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: 'bold',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 20,
    textAlign: 'center',
  },

  participantsContainer: {
    marginTop: 10,
  },
  participantSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  participantsList: {
    flexDirection: 'row',
  },
  participantItem: {
    alignItems: 'center',
    marginRight: 12,
    width: 60,
  },
  participantAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(78, 205, 196, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  liveAvatar: {
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
  },
  participantInitial: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  participantName: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 10,
    textAlign: 'center',
  },
});
