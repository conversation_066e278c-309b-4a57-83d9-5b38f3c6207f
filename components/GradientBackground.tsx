import React from 'react';
import { View, StyleSheet, Image } from 'react-native';

type GradientBackgroundProps = {
  showPrayerGradient?: boolean;
};

export default function GradientBackground({ showPrayerGradient = true }: GradientBackgroundProps) {
  if (!showPrayerGradient) return null;

  return (
    <View pointerEvents="none" style={StyleSheet.absoluteFill}>
      {/* Background Image */}
      <Image
        source={require('../assets/images/splash_bg_blur.png')}
        style={styles.backgroundImage}
        resizeMode="cover"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
  },
});
