import React from 'react';
import { View, Text } from 'react-native';
import Animated, { FadeInUp } from 'react-native-reanimated';
import Ionicons from '@expo/vector-icons/Ionicons';
import { textStyles, spacing } from '../lib/design';

export default function PrayerFeedHeader() {
  return (
    <Animated.View
      entering={FadeInUp.delay(100).duration(600)}
      style={{
        paddingHorizontal: spacing.md,
        marginBottom: spacing.md,
        marginTop: spacing.xs,
      }}
    >
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: spacing.sm,
      }}>
        <Ionicons
          name="heart"
          size={24}
          color="white"
          style={{ marginRight: spacing.sm }}
        />
        <Text style={[textStyles.h3, {
          color: 'white',
          fontWeight: '400',
          fontSize: 20,
        }]}>
          Community Prayer Feed
        </Text>
      </View>

      <Text style={[textStyles.bodySecondary, {
        color: 'rgba(255,255,255,0.8)',
        lineHeight: 20,
        fontSize: 15,
      }]}>
        Join others in prayer and share your heart with the community.
      </Text>
    </Animated.View>
  );
}
