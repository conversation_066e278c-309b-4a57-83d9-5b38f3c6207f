import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';

export type AppTopBarProps = {
  showBack?: boolean;
  onBackPress?: () => void;
  showNotifications?: boolean;
  onNotificationsPress?: () => void;
  rightIconName?: keyof typeof Ionicons.glyphMap | null;
  onRightIconPress?: () => void;
  rightComponent?: React.ReactNode;
};

export default function AppTopBar({
  showBack = false,
  onBackPress,
  showNotifications = false,
  onNotificationsPress,
  rightIconName = null,
  onRightIconPress,
  rightComponent,
}: AppTopBarProps) {
  const router = useRouter();

  const handleBack = () => {
    if (onBackPress) return onBackPress();
    router.back();
  };

  const handleNotifications = () => {
    if (onNotificationsPress) return onNotificationsPress();
    router.push('/notifications');
  };

  return (
    <View
      pointerEvents="box-none"
      style={{ position: 'absolute', left: 0, right: 0, top: 0, height: 105, zIndex: 50 }}
    >
      <LinearGradient
        colors={['#000000dd', '#00000088', '#00000000']}
        locations={[0, 0.55, 1]}
        style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0 }}
      />

      <View style={{ height: 105, paddingBottom: 12, paddingHorizontal: 16 }}>
        <View
          style={{
            marginTop: 45,
            height: 60,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          {/* Left slot - fixed width for consistent layout */}
          <View style={{ width: 120, alignItems: 'flex-start' }}>
            {showBack ? (
              <TouchableOpacity
                accessibilityLabel="Go back"
                onPress={handleBack}
                style={{ width: 48, height: 48, alignItems: 'center', justifyContent: 'center' }}
              >
                <Ionicons name="chevron-back" size={24} color="white" />
              </TouchableOpacity>
            ) : null}
          </View>

          {/* Center spacer for consistent layout (no text) */}
          <View style={{ flex: 1 }} />

          {/* Right slot - fixed width for consistent layout */}
          <View
            style={{
              width: 120,
              alignItems: 'flex-end',
              flexDirection: 'row',
              justifyContent: 'flex-end',
              gap: 8,
            }}
          >
            {rightComponent}
            {showNotifications && (
              <TouchableOpacity
                accessibilityLabel="Notifications"
                onPress={handleNotifications}
                style={{ width: 48, height: 48, alignItems: 'center', justifyContent: 'center' }}
              >
                <Ionicons name="notifications-outline" size={24} color="white" />
              </TouchableOpacity>
            )}
            {rightIconName && (
              <TouchableOpacity
                accessibilityLabel="Right action"
                onPress={onRightIconPress}
                style={{ width: 48, height: 48, alignItems: 'center', justifyContent: 'center' }}
              >
                <Ionicons name={rightIconName} size={24} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </View>
  );
}
