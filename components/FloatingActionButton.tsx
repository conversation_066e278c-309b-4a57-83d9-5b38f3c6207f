import React from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import { BlurView } from 'expo-blur';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring,
  withSequence,
  interpolate
} from 'react-native-reanimated';
import Ionicons from '@expo/vector-icons/Ionicons';
import { blur, spacing } from '../lib/design';

interface FloatingActionButtonProps {
  onPress: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  size?: number;
  style?: ViewStyle;
  accessibilityLabel?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export default function FloatingActionButton({
  onPress,
  icon = 'add',
  size = 56,
  style,
  accessibilityLabel = 'Create new prayer',
}: FloatingActionButtonProps) {
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 300 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  const handlePress = () => {
    // Add a subtle rotation animation on press
    rotation.value = withSequence(
      withSpring(15, { damping: 15, stiffness: 300 }),
      withSpring(0, { damping: 15, stiffness: 300 })
    );
    onPress();
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` }
      ],
    };
  });

  const iconSize = Math.round(size * 0.5);

  return (
    <AnimatedTouchableOpacity
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={[
        {
          position: 'absolute',
          bottom: spacing.xl + 90, // Above tab bar with extra clearance
          right: spacing.lg,
          width: size,
          height: size,
          borderRadius: size / 2,
          overflow: 'hidden',
          // Shadow for floating effect
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.3,
          shadowRadius: 16,
          elevation: 12,
          zIndex: 1000,
        },
        animatedStyle,
        style,
      ]}
      activeOpacity={0.8}
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel}
      accessibilityHint="Tap to create a new prayer"
    >
      <BlurView 
        intensity={blur.strong} 
        tint="dark"
        style={{
          flex: 1,
          backgroundColor: 'rgba(255,255,255,0.15)',
          borderWidth: 1,
          borderColor: 'rgba(255,255,255,0.3)',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Ionicons 
          name={icon} 
          size={iconSize} 
          color="white" 
        />
      </BlurView>
    </AnimatedTouchableOpacity>
  );
}
