import React from 'react';
import { View, Text, ViewStyle, TextStyle, StyleProp } from 'react-native';
import { componentStyles, textStyles } from '../../lib/design';

interface BadgeProps {
  text: string;
  variant?: 'success' | 'error' | 'warning' | 'info' | 'live' | 'category';
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

const Badge: React.FC<BadgeProps> = ({ text, variant = 'category', style, textStyle }) => {
  const baseBadgeStyle = [
    componentStyles.badge,
    variant === 'success' && componentStyles.badgeSuccess,
    variant === 'error' && componentStyles.badgeError,
    variant === 'warning' && componentStyles.badgeWarning,
    variant === 'info' && componentStyles.badgeInfo,
    variant === 'live' && componentStyles.badgeLive,
    variant === 'category' && componentStyles.badgeCategory,
  ];

  return (
    <View style={[...baseBadgeStyle, style]}>
      <Text style={[textStyles.badgeText, textStyle]}>{text}</Text>
    </View>
  );
};

export default Badge;
