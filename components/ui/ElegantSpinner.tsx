import React, { useEffect } from 'react';
import { View, Text } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
  Easing,
} from 'react-native-reanimated';
import { spacing, surfaces, withOpacity } from '../../lib/design';

interface ElegantSpinnerProps {
  size?: number;
  color?: string;
  text?: string;
  showText?: boolean;
}

export default function ElegantSpinner({
  size = 32,
  color = 'rgba(255,255,255,0.8)',
  text = 'Loading...',
  showText = false,
}: ElegantSpinnerProps) {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);

  useEffect(() => {
    // Smooth rotation animation
    rotation.value = withRepeat(
      withTiming(1, {
        duration: 1200,
        easing: Easing.linear,
      }),
      -1,
      false
    );

    // Subtle breathing scale animation
    scale.value = withRepeat(
      withTiming(1.1, {
        duration: 1500,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { rotate: `${rotation.value * 360}deg` },
        { scale: scale.value },
      ],
    };
  });

  const dotAnimatedStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      rotation.value,
      [0, 0.25, 0.5, 0.75, 1],
      [1, 0.3, 0.6, 0.9, 1]
    );
    return { opacity };
  });

  return (
    <View style={{ alignItems: 'center', gap: spacing.md }}>
      <Animated.View
        style={[
          {
            width: size,
            height: size,
            borderRadius: size / 2,
            borderWidth: 2,
            borderTopColor: color,
            borderRightColor: 'rgba(255,255,255,0.25)',
            borderBottomColor: 'rgba(255,255,255,0.15)',
            borderLeftColor: 'transparent',
            backgroundColor: 'transparent',
          },
          animatedStyle,
        ]}
      />
      
      {/* Elegant dots overlay */}
      <View
        style={{
          position: 'absolute',
          top: 0,
          width: size,
          height: size,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Animated.View
          style={[
            {
              width: 4,
              height: 4,
              borderRadius: 2,
              backgroundColor: color,
            },
            dotAnimatedStyle,
          ]}
        />
      </View>

      {showText && text && (
        <Text
          style={{
            color: 'rgba(255,255,255,0.7)',
            fontSize: 14,
            fontFamily: 'Roboto-Regular',
            textAlign: 'center',
          }}
        >
          {text}
        </Text>
      )}
    </View>
  );
}
