import React from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import { componentStyles, colors, surfaces } from '../../lib/design';
import { Ionicons } from '@expo/vector-icons';

export type IconButtonVariant = 'primary' | 'secondary' | 'destructive' | 'ghost';
export type IconButtonSize = 'sm' | 'md' | 'lg';

interface IconButtonProps {
  icon: keyof typeof Ionicons.glyphMap;
  onPress?: () => void;
  disabled?: boolean;
  variant?: IconButtonVariant;
  size?: IconButtonSize;
  style?: ViewStyle;
}

export default function IconButton({
  icon,
  onPress,
  disabled,
  variant = 'secondary',
  size = 'md',
  style,
}: IconButtonProps) {
  const sizeConfig = {
    sm: { buttonSize: 32, iconSize: 16 },
    md: { buttonSize: 40, iconSize: 20 },
    lg: { buttonSize: 48, iconSize: 24 },
  };

  const { buttonSize, iconSize } = sizeConfig[size];

  const baseButtonStyle = {
    width: buttonSize,
    height: buttonSize,
    borderRadius: buttonSize / 2,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderWidth: 1,
  };

  const variantStyle = (() => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: colors.primary[500],
          borderColor: colors.primary[600],
        };
      case 'secondary':
        return {
          backgroundColor: surfaces.cardHover,
          borderColor: surfaces.border,
        };
      case 'destructive':
        return {
          backgroundColor: `rgba(${colors.error[500]}, 0.15)`,
          borderColor: `rgba(${colors.error[500]}, 0.3)`,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: surfaces.cardHover,
          borderColor: surfaces.border,
        };
    }
  })();

  const getIconColor = () => {
    if (disabled) return surfaces.text.disabled;

    switch (variant) {
      case 'primary':
        return surfaces.text.primary;
      case 'destructive':
        return colors.error[400];
      default:
        return surfaces.text.secondary;
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.8}
      style={[
        baseButtonStyle,
        variantStyle,
        disabled && componentStyles.buttonDisabled,
        style,
      ]}
    >
      <Ionicons name={icon} size={iconSize} color={getIconColor()} />
    </TouchableOpacity>
  );
}
