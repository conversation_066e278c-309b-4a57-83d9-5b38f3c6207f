import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle } from 'react-native';
import { componentStyles, textStyles } from '../../lib/design';

interface ChipProps {
  label: string;
  selected?: boolean;
  onPress?: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  variant?: 'default' | 'category';
}

export default function Chip({
  label,
  selected,
  onPress,
  style,
  textStyle,
  variant = 'default',
}: ChipProps) {
  const baseChipStyle = [
    componentStyles.chip,
    componentStyles.chipSmall,
    selected && componentStyles.chipActive,
  ];

  const baseTextStyle = [
    textStyles.chipText,
    selected && textStyles.chipTextActive,
  ];

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.8}
      style={[...baseChipStyle, style]}
    >
      <Text style={[...baseTextStyle, textStyle]}>{label}</Text>
    </TouchableOpacity>
  );
}
