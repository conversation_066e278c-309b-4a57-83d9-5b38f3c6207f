import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle, ActivityIndicator } from 'react-native';
import { componentStyles, textStyles, colors, spacing } from '../../lib/design';
import { Ionicons } from '@expo/vector-icons';

export type ButtonVariant = 'primary' | 'secondary' | 'ghost' | 'success' | 'error' | 'warning';
export type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  title: string;
  onPress?: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: ButtonVariant;
  size?: ButtonSize;
  icon?: keyof typeof Ionicons.glyphMap;
  iconPosition?: 'left' | 'right';
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

export default function Button({
  title,
  onPress,
  disabled,
  loading,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  style,
  textStyle,
  fullWidth = false,
}: ButtonProps) {
  const iconSize = size === 'sm' ? 16 : size === 'md' ? 18 : 20;
  const isDisabled = disabled || loading;

  // Get base button styles
  const baseButtonStyle = [
    componentStyles.button,
    size === 'sm' && componentStyles.buttonSmall,
    size === 'lg' && componentStyles.buttonLarge,
    fullWidth && { width: '100%' },
  ];

  // Get variant styles
  const variantStyle = (() => {
    switch (variant) {
      case 'primary':
        return componentStyles.buttonPrimary;
      case 'secondary':
        return componentStyles.buttonSecondary;
      case 'ghost':
        return componentStyles.buttonGhost;
      case 'success':
        return componentStyles.buttonSuccess;
      case 'error':
        return componentStyles.buttonError;
      case 'warning':
        return componentStyles.buttonWarning;
      default:
        return componentStyles.buttonPrimary;
    }
  })();

  // Get text styles
  const baseTextStyle = [
    size === 'sm' ? textStyles.buttonTextSmall :
    size === 'lg' ? textStyles.buttonTextLarge :
    textStyles.buttonText,
  ];

  // Get loading indicator color
  const loadingColor = variant === 'ghost' ? colors.primary[400] : '#ffffff';

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
      style={[
        ...baseButtonStyle,
        variantStyle,
        isDisabled && componentStyles.buttonDisabled,
        { gap: spacing.sm },
        style,
      ]}
    >
      {loading ? (
        <ActivityIndicator size="small" color={loadingColor} />
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <Ionicons name={icon} size={iconSize} color={loadingColor} />
          )}
          <Text style={[...baseTextStyle, textStyle]}>{title}</Text>
          {icon && iconPosition === 'right' && (
            <Ionicons name={icon} size={iconSize} color={loadingColor} />
          )}
        </>
      )}
    </TouchableOpacity>
  );
}
