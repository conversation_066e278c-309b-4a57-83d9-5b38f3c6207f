import React from 'react';
import { ViewProps, StyleProp, ViewStyle } from 'react-native';
import { BlurView } from 'expo-blur';
import { componentStyles, blur } from '../../lib/design';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'small' | 'large' | 'light';
  padding?: boolean;
}

export default function Card({
  children,
  style,
  variant = 'default',
  padding = true,
  ...rest
}: CardProps) {
  const baseCardStyle = [
    componentStyles.card,
    variant === 'elevated' && componentStyles.cardElevated,
    variant === 'small' && componentStyles.cardSmall,
    variant === 'large' && componentStyles.cardLarge,
    variant === 'light' && componentStyles.card, // Light variant uses default card styling
    !padding && { padding: 0 },
    { overflow: 'hidden' as const },
  ];

  return (
    <BlurView intensity={blur.strong} tint="light" style={[...baseCardStyle, style]} {...rest}>
      {children}
    </BlurView>
  );
}
