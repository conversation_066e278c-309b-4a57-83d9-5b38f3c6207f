// ESLint v9 flat config using TypeScript + Prettier (Expo-friendly)
module.exports = [
  // Global ignores
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '.expo/**',
      'supabase/functions/**',
      'lib/supabase.generated.ts',
      'deprecated/**',
    ],
  },
  // TypeScript files
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: 'module',
      parser: require('@typescript-eslint/parser'),
      parserOptions: {
        project: './tsconfig.json',
      },
    },
    plugins: {
      '@typescript-eslint': require('@typescript-eslint/eslint-plugin'),
      prettier: require('eslint-plugin-prettier'),
      'unused-imports': require('eslint-plugin-unused-imports'),
    },
    rules: {
      'prettier/prettier': 'error',
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          ignoreRestSiblings: true,
          caughtErrors: 'all',
          caughtErrorsIgnorePattern: '^_',
        },
      ],
      'unused-imports/no-unused-imports': 'error',
      '@typescript-eslint/no-explicit-any': 'off',
      // Design system guards
      // 1) Soft-ban common hard-coded spacing values on RN style objects
      'no-restricted-syntax': [
        'warn',
        {
          selector:
            "Property[key.name=/^(margin.*|padding.*|gap)$/][value.type='Literal'][value.value=/^(10|12|18|20)$/]",
          message:
            'Use spacing tokens (spacing.xs/sm/md/lg/xl/2xl/...) instead of numeric spacing values.',
        },
      ],
      // 2) Soft-ban non-standard font weights when setting RN styles
      'no-restricted-properties': ['warn', { object: '*', property: 'fontWeight', message: "Use '300' for headers or '400' for body; avoid '500'/'600'/'700'." }],
    },
  },
  // JavaScript files
  {
    files: ['**/*.{js,jsx}'],
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: { jsx: true },
      },
    },
    plugins: {
      prettier: require('eslint-plugin-prettier'),
    },
    rules: {
      'prettier/prettier': 'error',
    },
  },
];
