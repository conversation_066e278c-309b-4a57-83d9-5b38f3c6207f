import React, { createContext, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { BroadcastParticipant } from '../lib/livekit';

export type BroadcastContextType = {
  // State
  isBroadcasting: boolean;
  isConnected: boolean;
  isJoining: boolean;
  isMicMuted: boolean;
  isPlaybackEnabled: boolean;
  duration: number; // seconds
  participants: BroadcastParticipant[];
  participantCount: number;
  error?: string;

  // Controls
  toggleMic: () => void;
  togglePlayback: () => void;
  setConnected: (v: boolean) => void;
  setBroadcasting: (v: boolean) => void;
  setJoining: (v: boolean) => void;
  setError: (e?: string) => void;
  setParticipants: (p: BroadcastParticipant[]) => void;
  reset: () => void;
};

const BroadcastContext = createContext<BroadcastContextType | undefined>(undefined);

export function useBroadcast(): BroadcastContextType {
  const ctx = useContext(BroadcastContext);
  if (!ctx) {
    // Safe fallback so UI can render even if provider is missing.
    return {
      isBroadcasting: false,
      isConnected: false,
      isJoining: false,
      isMicMuted: false,
      isPlaybackEnabled: true,
      duration: 0,
      participants: [],
      participantCount: 0,
      error: undefined,
      toggleMic: () => {},
      togglePlayback: () => {},
      setConnected: () => {},
      setBroadcasting: () => {},
      setJoining: () => {},
      setError: () => {},
      setParticipants: () => {},
      reset: () => {},
    };
  }
  return ctx;
}

export function BroadcastProvider({ children }: { children: React.ReactNode }) {
  const [isBroadcasting, setIsBroadcasting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [isMicMuted, setIsMicMuted] = useState(false);
  const [isPlaybackEnabled, setIsPlaybackEnabled] = useState(true);
  const [duration, setDuration] = useState(0);
  const [participants, setParticipants] = useState<BroadcastParticipant[]>([]);
  const [error, setError] = useState<string | undefined>(undefined);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  // Keep a simple timer while broadcasting
  useEffect(() => {
    if (isBroadcasting) {
      timerRef.current = setInterval(() => setDuration((d) => d + 1), 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      setDuration(0);
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [isBroadcasting]);

  const toggleMic = () => setIsMicMuted((m) => !m);
  const togglePlayback = () => setIsPlaybackEnabled((a) => !a);

  const reset = () => {
    setIsBroadcasting(false);
    setIsConnected(false);
    setIsJoining(false);
    setIsMicMuted(false);
    setIsPlaybackEnabled(true);
    setDuration(0);
    setParticipants([]);
    setError(undefined);
  };

  const value: BroadcastContextType = useMemo(
    () => ({
      isBroadcasting,
      isConnected,
      isJoining,
      isMicMuted,
      isPlaybackEnabled,
      duration,
      participants,
      participantCount: participants.length,
      error,
      toggleMic,
      togglePlayback,
      setConnected: setIsConnected,
      setBroadcasting: setIsBroadcasting,
      setJoining: setIsJoining,
      setError,
      setParticipants,
      reset,
    }),
    [
      isBroadcasting,
      isConnected,
      isJoining,
      isMicMuted,
      isPlaybackEnabled,
      duration,
      participants,
      error,
    ],
  );

  return <BroadcastContext.Provider value={value}>{children}</BroadcastContext.Provider>;
}
