import React, { createContext, useContext, useState, useCallback, ReactNode, useMemo } from 'react';
import { PrayerWithProfile } from '../lib/supabase';

interface PrayerContextType {
  prayers: Map<string, PrayerWithProfile>;
  updatePrayer: (prayerId: string, updates: Partial<PrayerWithProfile>) => void;
  setPrayers: (prayers: PrayerWithProfile[]) => void;
  getPrayer: (prayerId: string) => PrayerWithProfile | undefined;
}

const PrayerContext = createContext<PrayerContextType | undefined>(undefined);

interface PrayerProviderProps {
  children: ReactNode;
}

export function PrayerProvider({ children }: PrayerProviderProps) {
  const [prayers, setPrayersMap] = useState<Map<string, PrayerWithProfile>>(new Map());

  const updatePrayer = useCallback((prayerId: string, updates: Partial<PrayerWithProfile>) => {
    setPrayersMap((prev) => {
      const newMap = new Map(prev);
      const existingPrayer = newMap.get(prayerId);
      if (existingPrayer) {
        newMap.set(prayerId, { ...existingPrayer, ...updates });
      }
      return newMap;
    });
  }, []);

  const setPrayers = useCallback((prayersList: PrayerWithProfile[]) => {
    const newMap = new Map();
    prayersList.forEach((prayer) => {
      newMap.set(prayer.id, prayer);
    });
    setPrayersMap(newMap);
  }, []);

  const getPrayer = useCallback(
    (prayerId: string) => {
      return prayers.get(prayerId);
    },
    [prayers],
  );

  const value: PrayerContextType = useMemo(() => ({
    prayers,
    updatePrayer,
    setPrayers,
    getPrayer,
  }), [prayers, updatePrayer, setPrayers, getPrayer]);

  return <PrayerContext.Provider value={value}>{children}</PrayerContext.Provider>;
}

export function usePrayerContext() {
  const context = useContext(PrayerContext);
  if (context === undefined) {
    throw new Error('usePrayerContext must be used within a PrayerProvider');
  }
  return context;
}
